using Microsoft.Extensions.DependencyInjection;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Modularity;

namespace Notify.EntityFrameworkCore;

[DependsOn(
    typeof(NotifyDomainModule),
    typeof(AbpEntityFrameworkCoreModule)
)]
public class NotifyEntityFrameworkCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<NotifyDbContext>(options =>
        {
                /* Add custom repositories here. Example:
                 * options.AddRepository<Question, EfCoreQuestionRepository>();
                 */
                
                options.AddRepository<UserNotification, UserNotificationRepository>();
                options.AddRepository<Notification, NotificationRepository>();
        });
    }
}
