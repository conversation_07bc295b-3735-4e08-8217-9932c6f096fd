using System;
using Notify.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Notify.Notifications;

public class NotificationRepository : EfCoreRepository<INotifyDbContext, Notification, Guid>, INotificationRepository
{
    public NotificationRepository(IDbContextProvider<INotifyDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }
}