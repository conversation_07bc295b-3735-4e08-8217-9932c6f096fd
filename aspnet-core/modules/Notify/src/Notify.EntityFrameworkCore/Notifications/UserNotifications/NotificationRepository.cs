using System;
using Notify.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Notify.Notifications.UserNotifications;

public class UserNotificationRepository : EfCoreRepository<INotifyDbContext, UserNotification, Guid>, IUserNotificationRepository
{
    public UserNotificationRepository(IDbContextProvider<INotifyDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }
}