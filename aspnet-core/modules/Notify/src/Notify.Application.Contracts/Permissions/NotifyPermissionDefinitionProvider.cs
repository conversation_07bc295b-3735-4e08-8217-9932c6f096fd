using Notify.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace Notify.Permissions;

public class NotifyPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(NotifyPermissions.GroupName, L("Permission:Notify"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<NotifyResource>(name);
    }
}
