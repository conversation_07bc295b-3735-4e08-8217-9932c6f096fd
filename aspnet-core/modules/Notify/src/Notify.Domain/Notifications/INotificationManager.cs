using System.Collections.Generic;
using System.Threading.Tasks;
using Notify.Notifications.UserNotifications;

namespace Notify.Notifications;

public interface INotificationManager
{
    Task<(List<UserNotification> UserNotifications, Notification Notification)> CreateAsync(CreateNotificationModel model);
    Task SendUserNotificationAsync(UserNotification userNotification, Notification notification);
}