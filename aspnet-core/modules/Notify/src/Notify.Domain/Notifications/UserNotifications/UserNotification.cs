using System;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Timing;

namespace Notify.Notifications.UserNotifications;

public class UserNotification : CreationAuditedAggregateRoot<Guid>
{
    public Guid UserId { get; private set; }
    
    public Guid NotificationId { get; private set; }

    public string NotificationMethod { get; private set; }

    public bool? Success { get; private set; }

    public DateTime? CompletionDateTime { get; private set; }

    public string? FailureReason { get; private set; }

    protected UserNotification()
    {
    }

    public UserNotification(
        Guid id,
        Guid userId,
        Guid notificationId,
        string notificationMethod
    ) : base(id)
    {
        UserId = userId;
        NotificationId = notificationId;
        NotificationMethod = notificationMethod;
    }

    public void SetResult(IClock clock, bool success, string? failureReason = null)
    {
        if (CompletionDateTime.HasValue)
        {
            return;
        }

        CompletionDateTime = clock.Now;
        Success = success;
        FailureReason = failureReason;
    }
}