
namespace Notify.Provider.Email;

[Serializable]
public class CreateEmailNotificationEto : CreateNotificationModel
{
    public string Subject
    {
        get => this.GetSubject();
        set => this.SetSubject(value);
    }

    public string Body
    {
        get => this.GetBody();
        set => this.SetBody(value);
    }

    public CreateEmailNotificationEto(IEnumerable<Guid> userIds, string subject, string body) :
    base(NotifyProviderEmailConsts.NotificationMethodEmail, userIds)
    {
        Subject = subject;
        Body = body;
    }


    public CreateEmailNotificationEto(Guid userId, string subject, string body) :
        base(NotifyProviderEmailConsts.NotificationMethodEmail, userId)
    {
        Subject = subject;
        Body = body;
    }
}
