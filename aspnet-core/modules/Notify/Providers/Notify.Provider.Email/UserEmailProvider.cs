using Volo.Abp.DependencyInjection;

namespace Notify.Provider.Email;

public class UserEmailProvider : IUserEmailProvider, ITransientDependency
{
    private readonly IExternalAbpUserLookupServiceProvider _abpUserLookupServiceProvider;

    public UserEmailProvider(IExternalAbpUserLookupServiceProvider abpUserLookupServiceProvider)
    {
        _abpUserLookupServiceProvider = abpUserLookupServiceProvider;
    }

    public virtual async Task<string?> GetAsync(Guid userId)
    {
        var userData = await _abpUserLookupServiceProvider.FindByIdAsync(userId);

        if (userData is null || !userData.PhoneNumberConfirmed || userData.PhoneNumber.IsNullOrWhiteSpace())
            return null;

        return userData.Email;
    }
}
