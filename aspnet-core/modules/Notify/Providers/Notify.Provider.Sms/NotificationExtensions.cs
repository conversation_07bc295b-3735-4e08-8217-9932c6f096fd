using System;
using Notify.Notifications;

namespace Notify.Provider.Sms;

public static class NotificationExtensions
{
    public static void SetSmsData(this Notification notification, string text)
    {
        notification.SetDataValue(NotifyProviderSmsConsts.NotificationTextPropertyName, text);
    }

    public static string GetSmsText(this Notification notification)
    {
        
        var text =  (string?) notification.GetDataValue(NotifyProviderSmsConsts.NotificationTextPropertyName);

        if (text is null)
            throw new NotImplementedException();
        
        return text;
    }
}