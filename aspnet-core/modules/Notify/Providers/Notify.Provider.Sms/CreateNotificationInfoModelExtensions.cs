using System;
using Volo.Abp.Data;

namespace Notify.Provider.Sms;

public static class CreateNotificationModelExtensions
{
    public static void SetText(this CreateNotificationModel model, string text)
    {
        model.SetProperty(NotifyProviderSmsConsts.NotificationTextPropertyName, text);
    }

    public static string GetText(this CreateNotificationModel model)
    {
        var text = (string?)model.GetProperty(NotifyProviderSmsConsts.NotificationTextPropertyName);

        if (text is null)
            throw new NotImplementedException();
        
        return text;
    }

}