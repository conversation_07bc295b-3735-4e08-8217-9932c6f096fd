namespace Notify.Provider.FCM;

[Serializable]
public class CreateFCMNotificationEto : CreateNotificationModel
{
    public string Title
    {
        get => this.GetTitle();
        set => this.SetTitle(value);
    }

    public string Body
    {
        get => this.GetBody();
        set => this.SetBody(value);
    }


    public CreateFCMNotificationEto(string title, string body, IEnumerable<Guid> userIds) 
        : base(NotifyProviderFCMConsts.NotificationMethodFCM, userIds)
    {
        Title = title;
        Body = body;
    }

    public CreateFCMNotificationEto(Guid userId, string title, string body) 
        : base(NotifyProviderFCMConsts.NotificationMethodFCM, userId)
    {
        Title = title;
        Body = body;
    }
}
