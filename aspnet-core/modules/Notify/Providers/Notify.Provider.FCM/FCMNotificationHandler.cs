using Notify.Notifications.UserNotifications;
using Notify.Notifications;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus;

namespace Notify.Provider.FCM;

public class FCMNotificationHandler : ILocalEventHandler<CreateFCMNotificationEto>, ITransientDependency
{
    private readonly INotificationRepository _notificationRepository;
    private readonly IUserNotificationRepository _userNotificationRepository;
    private readonly FCMNotificationManager _fCMNotificationManager;

    public FCMNotificationHandler(
        INotificationRepository notificationRepository,
        IUserNotificationRepository userNotificationRepository,
        FCMNotificationManager fCMNotificationManager)
    {
        _notificationRepository = notificationRepository;
        _userNotificationRepository = userNotificationRepository;
        _fCMNotificationManager = fCMNotificationManager;
    }

    public async Task HandleEventAsync(CreateFCMNotificationEto eventData)
    {
        var result = await _fCMNotificationManager.CreateAsync(eventData);

        await _notificationRepository.InsertAsync(result.Notification, true);

        await _userNotificationRepository.InsertManyAsync(result.UserNotifications, true);
    }
}
