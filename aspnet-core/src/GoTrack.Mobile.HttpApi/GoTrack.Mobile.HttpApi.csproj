<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\common.props"/>

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <RootNamespace>GoTrack.Mobile</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\GoTrack.Mobile.Application.Contracts\GoTrack.Mobile.Application.Contracts.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="OpenIddict.Server" Version="5.5.0"/>
        <PackageReference Include="OpenIddict.Server.AspNetCore" Version="5.5.0"/>
        <PackageReference Include="Volo.Abp.Account.HttpApi" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.BlobStoring.FileSystem" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.OpenIddict.AspNetCore" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="8.3.0"/>
        <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="8.3.0"/>
    </ItemGroup>

</Project>
