using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using OpenIddict.Abstractions;
using OpenIddict.Server;
using OpenIddict.Server.AspNetCore;
using GoTrack.Mobile.MobileIdentityUser;
using GoTrack.Mobile.MobileIdentityUsers;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.OpenIddict;
using Volo.Abp.OpenIddict.ExtensionGrantTypes;
using Volo.Abp.Uow;
using IdentityUser = Volo.Abp.Identity.IdentityUser;

namespace GoTrack.Mobile;

public class OtpTokenExtensionGrant : ITokenExtensionGrant
{
    //https://community.abp.io/posts/how-to-add-a-custom-grant-type-in-openiddict.-6v0df94z
    public async Task<IActionResult> HandleAsync(ExtensionGrantContext context)
    {
        var phoneNumber = context.Request.GetParameter("phone_number")?.ToString();
        var otp = context.Request.GetParameter("otp").ToString();
        
        if (string.IsNullOrEmpty(otp) || string.IsNullOrEmpty(phoneNumber))
        {
            return new ForbidResult(
                new[] {OpenIddictServerAspNetCoreDefaults.AuthenticationScheme},
                properties: new AuthenticationProperties(new Dictionary<string, string>
                {
                    [OpenIddictServerAspNetCoreConstants.Properties.Error] = OpenIddictConstants.Errors.InvalidRequest
                }!));
        }
        
        
        var mobileIdentityUser = context.HttpContext.RequestServices.GetRequiredService<IMobileIdentityUserAppService>();
        var userRepository = context.HttpContext.RequestServices.GetRequiredService<IIdentityUserRepository>();
        
        string newCustomerUserName;

        try
        {
            newCustomerUserName = await mobileIdentityUser.CreateIfNotExistingAsync(new MobileUserSignInDto
            {
                Msisdn = phoneNumber,
                Otp = otp
            });
        }
        catch (BusinessException e)
        {
            //The ErrorDescription Here Is Not Localized. Please Check
            return new ForbidResult(
                new[] { OpenIddictServerAspNetCoreDefaults.AuthenticationScheme },
                properties: new AuthenticationProperties(new Dictionary<string, string>
                {
                    [OpenIddictServerAspNetCoreConstants.Properties.Error] = OpenIddictConstants.Errors.InvalidRequest,
                    [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = e.Code,
                }!));
        }
        
             
        var signInManager = context.HttpContext.RequestServices.GetRequiredService<SignInManager<IdentityUser>>();
        var userManager = context.HttpContext.RequestServices.GetRequiredService<IdentityUserManager>();
        var dataFilters =  context.HttpContext.RequestServices.GetRequiredService<IDataFilter>();
        var scopeManager =  context.HttpContext.RequestServices.GetRequiredService<IOpenIddictScopeManager>();
        var userClaimsPrincipalFactory = context.HttpContext.RequestServices.GetRequiredService<IUserClaimsPrincipalFactory<IdentityUser>>();
        
        IdentityUser? identityUser;
        using (dataFilters.Disable<IMultiTenant>())
        {
            identityUser = await userManager.FindByNameAsync(newCustomerUserName);
        }
        var scopes = context.Request.GetScopes();
        
        var claimsPrincipal = await userClaimsPrincipalFactory.CreateAsync(identityUser);
        claimsPrincipal.SetScopes(scopes);
        claimsPrincipal.SetResources(await GetResourcesAsync(scopes,scopeManager));
        claimsPrincipal.SetAudiences("GoTrackMobile");
        
        
        await context.HttpContext.RequestServices.GetRequiredService<AbpOpenIddictClaimsPrincipalManager>().HandleAsync(context.Request, claimsPrincipal);

        var result =  new Microsoft.AspNetCore.Mvc.SignInResult(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
            claimsPrincipal);

        var identitySecurityLogManager = context.HttpContext.RequestServices.GetRequiredService<IdentitySecurityLogManager>();
        await identitySecurityLogManager.SaveAsync(
            new IdentitySecurityLogContext
            {
                Identity = OpenIddictSecurityLogIdentityConsts.OpenIddict,
                Action = OpenIddictSecurityLogActionConsts.LoginSucceeded,
                UserName = identityUser.UserName,
                ClientId = context.Request.ClientId
            }
        );
        
        return result;
    }
    
    protected virtual async Task<IEnumerable<string>> GetResourcesAsync(ImmutableArray<string> scopes, IOpenIddictScopeManager ScopeManager)
    {
        var resources = new List<string>();
        if (!scopes.Any())
        {
            return resources;
        }

        await foreach (var resource in ScopeManager.ListResourcesAsync(scopes))
        {
            resources.Add(resource);
        }
        return resources;
    }


    public string Name { get; } = "otp";
}