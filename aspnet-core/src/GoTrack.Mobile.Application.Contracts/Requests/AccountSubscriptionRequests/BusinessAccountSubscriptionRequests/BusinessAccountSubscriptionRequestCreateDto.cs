using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using GoTrack.Mobile.Addresses;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public class BusinessAccountSubscriptionRequestCreateDto
{
    [Required]
    public string CompanyName { get; set; }

    [Required]
    public AddressFormDto Address { get; set; }

    [Required]
    public string AccountName { get; set; }

    [Required]
    public List<SubscriptionVehicleInfoCreateDto> SubscriptionVehicleInfoCreateDtos { get; set; }

    [Required] public string SubscriptionPlanKey { get; set; }
    [Required] public int UserCount { get; set; }
    public Guid? SmsBundleId { get; set; }
    [Required] public int SubscriptionDurationInMonths { get; set; }
    
    [StringLength(8, MinimumLength = 4)]
    public string? PromoCode { get; set; }

}