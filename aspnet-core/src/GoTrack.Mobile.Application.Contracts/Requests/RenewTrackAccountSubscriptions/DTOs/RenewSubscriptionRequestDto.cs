using System;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Mobile.Requests.RenewTrackAccountSubscriptions.DTOs;

public class RenewSubscriptionRequestDto : RequestDto
{
    public Guid TrackAccountId { get; set; }
    public string SubscriptionPlanKey { get;  set; }
    public  Guid TrackAccountSubscriptionId { get; set; }
    public int UserCount { get;set; }
    public Guid? SmsBundleId { get; set; }
    public int SubscriptionDurationInMonths { get; set; }
    public decimal? DiscountRate { get; set; }
    public RenewSubscriptionRequestStage RenewSubscriptionRequestStage { get; set; }
}