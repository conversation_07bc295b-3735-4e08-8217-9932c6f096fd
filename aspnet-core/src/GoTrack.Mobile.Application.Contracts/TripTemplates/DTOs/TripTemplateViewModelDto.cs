using GoTrack.Mobile.Routes.DTOs;
using GoTrack.Mobile.VehicleGroups;
using System;
using System.Collections.Generic;
using GoTrack.Mobile.Vehicles.DTOs;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.TripTemplates.DTOs;

public class TripTemplateViewModelDto : FullAuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public Guid TrackAccountId { get; set; }
    public List<RouteViewModelDto> Routes { get; set; } = new();
    public List<VehicleDto> Vehicles { get; set; } = new();
    public List<VehicleGroupDto> VehicleGroups { get; set; } = new();
}
