using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.VehicleGroups;

public interface IVehicleGroupAppService : IApplicationService
{
    Task<Guid> CreateAsync(VehicleGroupCreateDto createCreateDto);
    Task<VehicleGroupDetailsDto> GetAsync(Guid id);
    Task<PagedResultDto<VehicleGroupDto>> GetListAsync(PagedResultRequestDto requestDto);
}