using System;

namespace GoTrack.Mobile.Observations.DTOs;

public class ObservationViewModelDto
{
    public Guid TrackAccountId { get; set; }
    public string PhoneNumber { get; set; } = string.Empty;
    public string ObserverName { get; set; } = string.Empty;
    public string VehicleOrVehicleGroupName { get; set; } = string.Empty;
    public Guid VehicleOrGroupId { get; set; }
    public string ObservationType { get; set; } = string.Empty;
}
