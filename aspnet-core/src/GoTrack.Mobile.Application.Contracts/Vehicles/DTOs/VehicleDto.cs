using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.Vehicles.DTOs;

public class VehicleDto : AuditedEntityDto<Guid>
{
    /// <summary>
    /// Color is string a hexadecimal color code Like "0xFF479DEE"
    /// </summary>
    public string ColorHex { get; set; }
    public string LicensePlateSerial { get; set; }
    public string LicensePlateSubClass { get; set; }
    public double ConsumptionRate { get; set; }
}