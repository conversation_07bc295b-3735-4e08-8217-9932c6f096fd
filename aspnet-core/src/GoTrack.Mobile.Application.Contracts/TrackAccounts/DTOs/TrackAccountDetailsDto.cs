using GoTrack.Mobile.UserTrackAccountAssociations.DTOs;
using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Mobile.TrackAccounts.DTOs;

public class TrackAccountDetailsDto : CreationAuditedEntityDto<Guid>
{
    public string AccountType { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public CurrentTrackAccountSubscriptionPlanDto CurrentTrackAccountSubscriptionPlan { get; set; }
    public int AdditionalUsers { get; set; }
}
