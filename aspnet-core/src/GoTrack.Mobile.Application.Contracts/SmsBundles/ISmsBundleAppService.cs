using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Mobile.SmsBundles.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.SmsBundles;

public interface ISmsBundleAppService : IApplicationService
{
    Task<SmsBundleDto> GetAsync(Guid id);
    Task<PagedResultDto<SmsBundleDto>> GetListAsync(PagedResultRequestDto input);
}