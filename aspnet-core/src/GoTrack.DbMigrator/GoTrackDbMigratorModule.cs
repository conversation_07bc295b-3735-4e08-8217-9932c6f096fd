using System.Diagnostics;
using GoTrack.EntityFrameworkCore;
using GoTrack.Identity;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Autofac;
using Volo.Abp.Data;
using Volo.Abp.Modularity;

namespace GoTrack.DbMigrator;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(GoTrackEntityFrameworkCoreModule),
    typeof(GoTrackApplicationContractsModule)
    )]
public class GoTrackDbMigratorModule : AbpModule
{

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpDataFilterOptions>(options =>
        {
            options.DefaultStates[typeof(IHostTenantUserFilter)] = new DataFilterState(isEnabled: false);
            options.DefaultStates[typeof(ICustomerUserFilter)] = new DataFilterState(isEnabled: false);
        });
        base.ConfigureServices(context);
    }
}
