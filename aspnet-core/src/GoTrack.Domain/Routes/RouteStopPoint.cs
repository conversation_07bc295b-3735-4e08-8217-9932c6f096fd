using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Values;

namespace GoTrack.Routes;

public class RouteStopPoint : ValueObject
{
    public Guid RouteId { get; private set; }
    public Guid StopPointId { get; private set; }

    private RouteStopPoint() { }

    internal RouteStopPoint(Guid routeId, Guid stopPointId)
    {
        RouteId = routeId;
        StopPointId = stopPointId;
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return RouteId;
        yield return StopPointId;
    }
}
