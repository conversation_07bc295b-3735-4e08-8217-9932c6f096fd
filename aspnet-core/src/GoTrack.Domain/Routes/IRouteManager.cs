using GoTrack.StopPoints;
using NetTopologySuite.Geometries;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;
using Point = NetTopologySuite.Geometries.Point;

namespace GoTrack.Routes;

public interface IRouteManager : IDomainService
{
    Task<Route> AddStopPointAsync(Guid routeId, StopPoint stopPoint);
    Task<Route> CreateAsync(string name, LineString line, Color color, Point startPoint, Point endPoint, Guid trackAccountId, List<StopPoint> stopPoints);
}
