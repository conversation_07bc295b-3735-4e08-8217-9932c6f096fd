using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Devices;
using GoTrack.TrackAccounts;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles.LicensePlates;
using MassTransit;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Vehicles;

public class VehicleManager : DomainService, IVehicleManager
{
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly IRepository<VehicleGroupVehicle, Guid> _vehicleGroupVehicleRepository;

    protected IRepository<VehicleDeviceEventLog, Guid> VehicleDeviceEventLogRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<VehicleDeviceEventLog, Guid>>();

    protected IDeviceManager DeviceManager =>
        LazyServiceProvider.LazyGetRequiredService<IDeviceManager>();

    protected IRepository<AlertTrigger, Guid> AlertTriggerRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<AlertTrigger, Guid>>();

    protected IPublishEndpoint PublishEndpoint =>
        LazyServiceProvider.LazyGetRequiredService<IPublishEndpoint>();

    protected IAlertTriggerManagerResolver AlertTriggerManagerResolver =>
        LazyServiceProvider.LazyGetRequiredService<IAlertTriggerManagerResolver>();


    public VehicleManager(
        IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<Vehicle, Guid> vehicleRepository,
        IRepository<VehicleGroupVehicle, Guid> vehicleGroupVehicleRepository)
    {
        _trackAccountRepository = trackAccountRepository;
        _vehicleRepository = vehicleRepository;
        _vehicleGroupVehicleRepository = vehicleGroupVehicleRepository;
    }

    public async Task<Vehicle> CreateAsync(Guid trackAccountId, LicensePlate licensePlate, Color color, double consumptionRate)
    {
        await ValidateLicensePlateAsync(licensePlate);

        var trackAccount = await _trackAccountRepository.GetAsync(trackAccountId);
        
        var vehicle = new Vehicle(GuidGenerator.Create(), trackAccount, licensePlate, color, consumptionRate);

        return vehicle;
    }

    public async Task AddVehicleToGroup(Guid vehicleId, Guid vehicleGroupId)
    {
        await _vehicleRepository.GetAsync(vehicleId);

        if (await _vehicleGroupVehicleRepository.AnyAsync(x => x.VehicleId == vehicleId && x.VehicleGroupId == vehicleGroupId))
            throw new BusinessException(GoTrackDomainErrorCodes.VehicleAlreadyExistsInThisGroup);

        var newVehicleGroupVehicle = new VehicleGroupVehicle(GuidGenerator.Create(), vehicleId, vehicleGroupId);

        await _vehicleGroupVehicleRepository.InsertAsync(newVehicleGroupVehicle);
    }

    public async Task SetVehiclesDevicesDeactiveAsync(Guid trackAccountId)
    {
        await _trackAccountRepository.GetAsync(trackAccountId);

        var vehicleQuery = await _vehicleRepository.GetQueryableAsync();

        var vehicleIdQuery = vehicleQuery.Where(x => x.TrackAccountId == trackAccountId)
            .Select(x => x.Id);

        var vehicleIds = await AsyncExecuter.ToListAsync(vehicleIdQuery);

        var alertTriggers = await AlertTriggerRepository.GetListAsync(x => vehicleIds.Contains(x.VehicleId));

        foreach (var alertTrigger in alertTriggers)
        {
            var alertTriggerManager = AlertTriggerManagerResolver.GetAlertTriggerManager(alertTrigger.Type);

            var alertCrud = await alertTriggerManager.GenerateAlertCrudAsync(alertTrigger, CrudType.Delete);

            await PublishEndpoint.Publish(alertCrud);
        }

        var vehicleDeviceEventLogQuery = await VehicleDeviceEventLogRepository.GetQueryableAsync();

        var deviceIdsQuery = vehicleDeviceEventLogQuery.Where(x => vehicleIds.Contains(x.VehicleId))
            .Select(x => x.DeviceId);

        var deviceIds = await AsyncExecuter.ToListAsync(deviceIdsQuery);

        foreach (var deviceId in deviceIds)
            await DeviceManager.SetDeviceDeactiveAsync(deviceId);
    }

    private async Task ValidateLicensePlateAsync(LicensePlate licensePlate)
    {
        var isAlreadyExist = await _vehicleRepository.AnyAsync(i =>
            i.LicensePlate.Serial == licensePlate.Serial
            && i.LicensePlate.SubClass == licensePlate.SubClass
        );

        if (isAlreadyExist)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateLicensePlate);
        }
    }

}