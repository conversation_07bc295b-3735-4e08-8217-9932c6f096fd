using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GoTrack.Observations.ObservationViewModels;

public interface IObservationReadRepository
{
    Task<(int totalCount, List<ObservationViewModel> observationViewModels)> GetObserverByUserTrackAccountAssociationIdAsync(Guid userTrackAccountAssociationId, int skipCount, int maxResultCount);
    Task<List<Guid>> GetObserverVehiclesIdsAsync(Guid userTrackAccountAssociationId);
    Task<List<VehicleViewModel>> GetObserverVehiclesIdWithLicensePlateAsync(Guid userTrackAccountAssociationId);
}
