using GoTrack.Vehicles;
using GoTrack.Vehicles.LicensePlates;
using System;
using System.Drawing;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Observations.ObservationViewModels;

public class VehicleViewModel : FullAuditedEntity<Guid>
{
    public Color Color { get; set; }
    public Guid VehicleId { get; private set; }
    public LicensePlate LicensePlate { get; private set; }
    public double ConsumptionRate { get; private set; }
    
    public VehicleViewModel(
        Guid id,
        LicensePlate licensePlate,
        Color color,
        double consumptionRate,
        Guid? creatorId,
        DateTime creationTime,
        Guid? deleterId,
        DateTime? deletionTime,
        bool isDeleted,
        Guid? lastModifierId,
        DateTime? lastModificationTime)
        : base(id)
    {
        CreatorId = creatorId;
        CreationTime = creationTime;
        DeleterId = deleterId;
        DeletionTime = deletionTime;
        IsDeleted = isDeleted;
        LastModifierId = lastModifierId;
        LastModificationTime = lastModificationTime;

        Color = color;
        LicensePlate = licensePlate;
        ConsumptionRate = consumptionRate;
    }

    public static VehicleViewModel GetVehicleViewModelFromVehicle(Vehicle vehicle)
    {
        return new VehicleViewModel
        (
            vehicle.Id,
            vehicle.LicensePlate,
            vehicle.Color,
            vehicle.ConsumptionRate,
            vehicle.CreatorId,
            vehicle.CreationTime,
            vehicle.DeleterId,
            vehicle.DeletionTime,
            vehicle.IsDeleted,
            vehicle.LastModifierId,
            vehicle.LastModificationTime
        );
    }
}
