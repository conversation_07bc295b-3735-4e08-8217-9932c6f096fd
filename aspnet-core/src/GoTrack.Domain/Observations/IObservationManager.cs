using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.Observations;

public interface IObservationManager : IDomainService
{
    Task<ObservationVehicle> AddVehicleAsync(Guid userTrackAccountAssociationId, Guid vehicleId);
    Task<ObservationVehicleGroup> AddVehicleGroupAsync(Guid userTrackAccountAssociationId, Guid vehicleGroupId);
    Task<ObservationVehicle> CreateObservationVehicleAsync(Guid userTrackAccountAssociationId, Guid vehicleId);
    Task<ObservationVehicleGroup> CreateObservationVehicleGroupAsync(Guid userTrackAccountAssociationId, Guid vehicleGroupId);
    Task DeleteObservationVehicleAsync(Guid userTrackAccountAssociationId, Guid vehicleId);
    Task DeleteObservationVehicleGroupAsync(Guid userTrackAccountAssociationId, Guid vehicleGroupId);
}
