using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.UserDeviceTokens;

public class UserDeviceToken : FullAuditedAggregateRoot<Guid>
{
    public Guid UserId { get; private set; }
    public string DeviceToken { get; private set; }

    public UserDeviceToken(Guid id, Guid userId, string deviceToken) : base(id)
    {
        UserId = userId;
        DeviceToken = deviceToken;
    }
}