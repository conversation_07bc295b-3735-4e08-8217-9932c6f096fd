using System;
using GoTrack.AlertDefinitions;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Devices;
using GoTrack.TrackAccounts;
using GoTrack.Vehicles;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Alerts.AlertLogs;

public class AlertLog : FullAuditedAggregateRoot<Guid> , IHaveTrackAccount
{
    public Guid VehicleId { get; private set; }
    public Guid DeviceId { get; private set; }
    public AlertType AlertType { get; private set; }
    public Guid AlertTriggerId { get; private set; }
    public DateTime StartedAt { get; private set; }
    public DateTime? EndedAt { get; private set; }
    public Guid TrackAccountId { get; private set; }
    

    //navigation prop
    public AlertTrigger AlertTrigger { get; private set; }
    public Vehicle Vehicle { get; private set; }
    public Device Device { get; private set; }

    private AlertLog()
    {
    }

    internal AlertLog(Guid id, Guid vehicleId, Guid deviceId, AlertType alertType, Guid alertTriggerId,Guid trackAccountId, DateTime startedAt,
        DateTime? endedAt) : base(id)
    {
        VehicleId = vehicleId;
        DeviceId = deviceId;
        AlertType = alertType;
        AlertTriggerId = alertTriggerId;
        if (startedAt > endedAt)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange); 
        }

        StartedAt = startedAt;
        EndedAt = endedAt;
        TrackAccountId = trackAccountId;
    }

    public void SetStartedAt(DateTime startedAt)
    {
        if (startedAt > StartedAt)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange); 
        }

        StartedAt = startedAt;
    }

    public void SetEndedAt(DateTime endedAt)
    {
        if (endedAt < EndedAt)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidDateRange); 
        }
        EndedAt = endedAt;
    }
}