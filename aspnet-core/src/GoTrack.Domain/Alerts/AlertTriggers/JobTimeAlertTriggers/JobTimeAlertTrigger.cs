using System.Collections.Generic;
using System;
using GoTrack.AlertDefinitions;

namespace GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers;

public class JobTimeAlertTrigger : AlertTrigger
{
    public string Name { get; private set; }
    public TimeOnly StartTime { get; private set; }
    public TimeOnly EndTime { get; private set; }

    public IReadOnlyCollection<DayOfWeek> DaysOfWeek => _daysOfWeek.AsReadOnly();
    private readonly List<DayOfWeek> _daysOfWeek = [];

    private JobTimeAlertTrigger() { }

    public JobTimeAlertTrigger(
        Guid id,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        Guid alertDefinitionId,
        Guid vehicleId,
        string name,
        TimeOnly startTime,
        TimeOnly endTime,
        List<DayOfWeek> daysOfWeek
    ) : base(
            id,
            AlertType.JobTime,
            notificationMethods,
            trackAccountId,
            alertDefinitionId,
            vehicleId
        )
    {
        Name = name;
        StartTime = startTime;
        EndTime = endTime;
        _daysOfWeek = daysOfWeek;
    }
}
