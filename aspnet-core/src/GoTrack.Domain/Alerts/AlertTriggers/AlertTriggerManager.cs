using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.Localization;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Warp10Abstraction.WarpLibs;
using System.Linq;


namespace GoTrack.Alerts.AlertTriggers;

public abstract class AlertTriggerManager : DomainService
{
    protected IRepository<AlertTrigger, Guid> AlertTriggerRepository;
    protected IRepository<AlertDefinitionAssociation, Guid> AlertDefinitionAssociationRepository;
    protected IRepository<VehicleDeviceEventLog, Guid> VehicleDeviceEventLogRepository;
    protected IRepository<AlertDefinition, Guid> AlertDefinitionRepository;
    protected IPublishEndpoint PublishEndpoint;
    protected IStringLocalizer Localizer;
    protected IWarpLib WarpLib => LazyServiceProvider.LazyGetRequiredService<IWarpLib>();

    public AlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory)
    {
        AlertTriggerRepository = alertTriggerRepository;
        AlertDefinitionAssociationRepository = alertDefinitionAssociationRepository;
        VehicleDeviceEventLogRepository = vehicleDeviceEventLogRepository;
        AlertDefinitionRepository = alertDefinitionRepository;
        PublishEndpoint = publishEndpoint;
        Localizer = stringLocalizerFactory.Create(typeof(GoTrackResource));
    }

    public async Task DeleteAsync(Guid alertDefinitionId)
    {
        var alertTriggers = await AlertTriggerRepository.GetListAsync(x => x.AlertDefinitionId == alertDefinitionId);
        foreach (var alertTrigger in alertTriggers)
        {
            await AlertTriggerRepository.DeleteAsync(alertTrigger);
            var alertCrud = await GenerateAlertCrudAsync(alertTrigger, CrudType.Delete);
            await PublishEndpoint.Publish(alertCrud);
        }
    }

    public virtual async Task CreateAsync(Guid alertDefinitionId)
    {
        var enteringZoneAlertDefinition = await AlertDefinitionRepository.GetAsync(alertDefinitionId);

        var query = await AlertDefinitionAssociationRepository.GetQueryableAsync();

        var vehicleQuery = query.Where(x => x.AlertDefinitionId == alertDefinitionId).GetUniqueVehiclesAsync();

        var uniqueVehicles = await AsyncExecuter.ToListAsync(vehicleQuery);

        var alertTriggers = GenerateAlertTriggers(enteringZoneAlertDefinition, uniqueVehicles);

        await AlertTriggerRepository.InsertManyAsync(alertTriggers);

        foreach (var trigger in alertTriggers)
        {
            var alertCrud = await GenerateAlertCrudAsync(trigger, CrudType.Add);
            await PublishEndpoint.Publish(alertCrud);
        }
    }

    public abstract Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime);

    public abstract List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles);

    public abstract Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType);
}