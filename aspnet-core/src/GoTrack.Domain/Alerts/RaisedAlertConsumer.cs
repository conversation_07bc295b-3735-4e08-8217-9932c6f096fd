using GoTrack.Alerts.Contracts.Exchanges.RaisedAlert;
using GoTrack.Alerts.Notifications;
using MassTransit;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.Alerts;

public class RaisedAlertConsumer : DomainService, IConsumer<RaisedAlert>
{
    private readonly RaisedAlertNotificationManager _raisedAlertNotificationManager;
    private readonly ActivitySource _activitySource;
    public RaisedAlertConsumer(RaisedAlertNotificationManager raisedAlertNotificationManager, ActivitySource activitySource)
    {
        _raisedAlertNotificationManager = raisedAlertNotificationManager;
        _activitySource = activitySource;
    }

    public async Task Consume(ConsumeContext<RaisedAlert> context)
    {
        using var activity = _activitySource.StartActivity();
        
        try
        {
            activity?.SetTag("message", context.Message);
            activity?.SetTag("message.alert_trigger_id", context.Message.AlertTriggerId);
            activity?.SetTag("message.start_timestamp", context.Message.StartTimestamp);
            activity?.SetTag("message.end_timestamp", context.Message.EndTimestamp);

            await _raisedAlertNotificationManager.HandleRaisedAlertAsync(context.Message);
            
            activity?.SetStatus(ActivityStatusCode.Ok);
        }
        catch (Exception ex) 
        {
            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
            activity?.AddException(ex);
            
            throw;
        }
    }
}