using System.Collections.Generic;

namespace GoTrack.BlobValidator;

public interface IFileTypeInspector
{
    /// <summary>
    /// Gets the file extension from the file's byte array.
    /// </summary>
    /// <param name="fileData">The file data as a byte array.</param>
    /// <returns>The file extension.</returns>
    string GetFileExtension(byte[] fileData);
    
    /// <summary>
    /// Gets the MIME type from the file's byte array.
    /// </summary>
    /// <param name="fileData">The file data as a byte array.</param>
    /// <returns>The MIME type.</returns>
    string GetMimeType(byte[] fileData);
    
    /// <summary>
    /// Gets a list of all extensions from the MIME definitions.
    /// </summary>
    /// <returns>A list of all file extensions.</returns>
    IEnumerable<string> GetAllExtensions();
}