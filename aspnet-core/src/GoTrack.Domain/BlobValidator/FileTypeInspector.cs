using System.Collections.Generic;
using System.Linq;
using MimeDetective;
using Volo.Abp.DependencyInjection;

namespace GoTrack.BlobValidator;

public class FileTypeInspector : IFileTypeInspector, ISingletonDependency
{
    private readonly IContentInspector _inspector;


    public FileTypeInspector()
    {
        _inspector = new ContentInspectorBuilder
        {
            Definitions = MimeDetective.Definitions.DefaultDefinitions.All()
        }.Build();
    }
    
    public string GetFileExtension(byte[] fileData)
    {
        var results = _inspector.Inspect(fileData);
        var result = results.ByFileExtension().FirstOrDefault();
        return result?.Extension ?? string.Empty;
    }
    
    public string GetMimeType(byte[] fileData)
    {
        var results = _inspector.Inspect(fileData);
        var result = results.ByMimeType().FirstOrDefault();
        return result?.MimeType ?? string.Empty;
    }
    
    public IEnumerable<string> GetAllExtensions()
    {
        return MimeDetective.Definitions.DefaultDefinitions
            .All()
            .SelectMany(d => d.File.Extensions)
            .Distinct();
    }  
}