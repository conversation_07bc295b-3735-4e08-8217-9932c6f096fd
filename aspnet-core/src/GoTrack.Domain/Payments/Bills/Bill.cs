using System;
using System.Collections.Generic;
using System.Linq;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.Payments.Bills;

public class Bill : CreationAuditedAggregateRoot<Guid>
{
    public Guid UserId { get; private set; }
    public BillStatus Status { get; private set; }
    public PaymentMethod PaymentMethod { get; private set; }
    public DateTime? PaymentDate { get; private set; }
    public IReadOnlyList<AppliedDiscount> AppliedDiscounts => _appliedDiscounts.AsReadOnly();
    private readonly List<AppliedDiscount> _appliedDiscounts = [];
    public IReadOnlyList<BillLineItem> BillLineItems => _billLineItems.AsReadOnly();
    private readonly List<BillLineItem> _billLineItems = [];
    
    public decimal BillableAmount { get; private set; }
    
    private Bill()
    {
    }

    internal Bill(Guid id, Guid userId, List<BillLineItem> billLineItems) : base(id)
    {
        UserId = userId;
        Status = BillStatus.Draft;
        _billLineItems = billLineItems;
        BillableAmount = Math.Round(CalculateTotal());
    }


    public decimal CalculateTotalBeforeDiscounts()
    {
        return BillLineItems.Sum(item => item.BillableAmount);
    }


    public void AddBillLineItem(BillLineItem billLineItem)
    {
        if (Status is BillStatus.Draft)
        {
            _billLineItems.Add(billLineItem);
            RecalculateTotal();
        }
        else
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotAddBillLineItem);
        }
    }

    public void ApplyDiscount(AppliedDiscount appliedDiscount)
    {
        if (Status is BillStatus.Draft)
        {
            _appliedDiscounts.Add(appliedDiscount);
            RecalculateTotal();
        }
        else
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotApplyDiscountToBill);
        }
    }
    
    public void Approve()
    {
        if (Status is not BillStatus.Draft)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotApproveBill);
        }

        Status = BillStatus.Approved;
    }

    public void Reject()
    {
        if (Status is not BillStatus.Draft)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotRejectBill);
        }

        Status = BillStatus.Rejected;
    }

    internal void MarkAsPaid(PaymentMethod paymentMethod, DateTime paymentDate)
    {
        if (Status is not BillStatus.Approved)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotMarkBillAsPaid);
        }

        PaymentMethod = paymentMethod;
        PaymentDate = paymentDate;
        Status = BillStatus.Paid;
    }

    private void RecalculateTotal()
    {
        BillableAmount = Math.Round(CalculateTotal());
    }
    private decimal CalculateTotal()
    {
        var price = BillLineItems.Sum(item => item.BillableAmount);

        foreach (var discount in AppliedDiscounts.OrderByDescending(x => x.TargetType))
        {
            price -= discount.GetDiscountAmount();
            if (price < 0)
            {
                price = 0;
                break;
            }
        }

        return price;
    }
}