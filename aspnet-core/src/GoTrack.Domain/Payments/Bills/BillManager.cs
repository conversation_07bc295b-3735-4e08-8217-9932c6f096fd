using GoTrack.Payments.Discounts;
using GoTrack.Requests;
using System;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Payments.Bills;

public class BillManager : DomainService
{
    private readonly IRepository<Bill, Guid> _billRepository;

    protected IRepository<Request, Guid> RequestRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Request, Guid>>();

    protected DiscountManager DiscountManager =>
        LazyServiceProvider.LazyGetRequiredService<DiscountManager>();

    public BillManager(IRepository<Bill, Guid> billRepository)
    {
        _billRepository = billRepository;
    }

    public async Task<Bill> GetByRequestIdAsync(Guid requestId)
    {
        var request = await RequestRepository.GetAsync(requestId);

        if (request is not IBillableRequest billableRequest)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RequestDoesNotSupportPayments);
        }

        if (billableRequest.BillId is null)
        {
            throw new EntityNotFoundException(nameof(Bill));
        }

        return await _billRepository.GetAsync(billableRequest.BillId.Value);
    }

    public async Task<Guid> CreateBillAsync(BillPlan billPlan, bool isApproved = false)
    {
        var bill = new Bill(
            GuidGenerator.Create(),
            billPlan.RequestOwnerId,
            billPlan.BillLineItems
        );

        var appliedDiscountForBill = await DiscountManager.CreateAppliedDiscountForBill(billPlan);

        appliedDiscountForBill.ForEach(a => bill.ApplyDiscount(a));

        if (isApproved)
            bill.Approve();

        await _billRepository.InsertAsync(bill);
        return bill.Id;
    }

    public async Task<Bill> CreateTempBillAsync(BillPlan billPlan)
    {
        var bill = new Bill(
            GuidGenerator.Create(),
            billPlan.RequestOwnerId,
            billPlan.BillLineItems
        );

        var appliedDiscountForBill = await DiscountManager.CreateAppliedDiscountForBill(billPlan);

        appliedDiscountForBill.ForEach(a => bill.ApplyDiscount(a));

        return bill;
    }
    
    public async Task MarkBillAsPaidAsync(Guid billId, PaymentMethod paymentMethod, DateTime paymentDate)
    {
        var bill = await _billRepository.GetAsync(billId);

        bill.MarkAsPaid(paymentMethod, paymentDate);

        await _billRepository.UpdateAsync(bill);

        await DiscountManager.IncrementPromoCodeUsageForPaidBillAsync(bill);
    }
}