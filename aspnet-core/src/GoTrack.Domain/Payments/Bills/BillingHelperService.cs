using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Payments.PricingItems;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Payments.Bills;

public class BillingHelperService : DomainService
{
    private readonly PricingManager _pricingManager;
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepo;

    public BillingHelperService(PricingManager pricingManager, IRepository<SmsBundle, Guid> smsBundleRepo)
    {
        _pricingManager = pricingManager;
        _smsBundleRepo = smsBundleRepo;
    }

    public async Task<BillLineItem> GetLineItemAsync(string itemKey, int quantity,
        int? subscriptionDurationInMonths)
    {
        var pricingItem = await _pricingManager.GetPriceAsync(itemKey);
        return new BillLineItem(
            pricingItem.Key,
            pricingItem.CurrentPrice,
            quantity,
            pricingItem.PricingType,
            pricingItem.PricingType == PricingType.Monthly ? subscriptionDurationInMonths : null
        );
    }

    public async Task<BillLineItem> GetSmsBundleLineItemAsync(Guid smsBundleId, int subscriptionDurationInMonths)
    {
        var smsBundle = await _smsBundleRepo.GetAsync(smsBundleId);
        return new BillLineItem(
            PricingItemKeys.SmsBundle,
            smsBundle.Price,
            1,
            PricingType.Monthly,
            subscriptionDurationInMonths
        );
    }
}