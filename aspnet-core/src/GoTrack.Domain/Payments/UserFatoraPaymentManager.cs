using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Fatora.Abstractions;
using Fatora.Abstractions.DTO.CreatePaymentDtos;
using Fatora.Abstractions.DTO.RevesalPaymentDto;
using Fatora.Abstractions.Enums;
using Fatora.Abstractions.Exceptions;
using Fatora.Options;
using GoTrack.Payments.Bills;
using GoTrack.Providers;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.AddVehiclesRequests;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Requests.SmsBundleRenewalRequests;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;
using FatoraPaymentStatus = Fatora.Abstractions.Enums.PaymentStatus;

namespace GoTrack.Payments;

public class UserFatoraPaymentManager : DomainService, IUserFatoraPaymentManager
{
    private readonly IFatoraService _fatoraService;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IIdentityUserRepository _userRepository;
    private readonly IBaseUrlProvider _baseUrlProvider;
    private readonly FatoraOptions _fatoraOptions;
    private readonly RequestManager _requestManager;
    protected BillManager BillManager =>
        LazyServiceProvider.LazyGetRequiredService<BillManager>();
    public UserFatoraPaymentManager
    (
        IFatoraService fatoraService,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository,
        IRepository<Request, Guid> requestRepository,
        IIdentityUserRepository userRepository,
        IBaseUrlProvider baseUrlProvider,
        IOptions<FatoraOptions> fatoraOptions, 
        RequestManager requestManager)
    {
        _fatoraService = fatoraService;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _requestRepository = requestRepository;
        _userRepository = userRepository;
        _baseUrlProvider = baseUrlProvider;

        _requestManager = requestManager;
        _fatoraOptions = fatoraOptions.Value;
    }

    public async Task<string> PayAsync(
        Guid requestId,
        Guid userId,
        string language,
        int amount,
        bool savedCards,
        string? callBackUrl,
        string? notes
    )
    {
        await _userRepository.GetAsync(userId);

        var request = await _requestRepository.GetAsync(x => x.Id == requestId && x.CreatorId == userId);
        if (request.Status is not (RequestStatus.Pending or RequestStatus.Processing))
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidRequestStatus);

        var newUserFatoraPayment = new UserFatoraPayment(
            GuidGenerator.Create(),
            userId,
            requestId,
            amount,
            callBackUrl,
            notes,
            uint.Parse(_fatoraOptions.TerminalId),
            PaymentStatus.Pending,
            request.Type
        );

        await _userFatoraPaymentRepository.InsertAsync(newUserFatoraPayment);

        var baseUrl = _baseUrlProvider.GetBaseUrl();

        var api = "/api/app/payment/checkPaymentStatus/"; // TODO 

        var triggerUrl = baseUrl + api + newUserFatoraPayment.Id;

        var createPaymentRequestDto = new CreatePaymentRequestDto(ConvertStringToFatoraLanguage(language), amount,
            callBackUrl, triggerUrl, false, userId, notes);

        try
        {
            var createPaymentResponseData = await _fatoraService.CreatePaymentAsync(createPaymentRequestDto);
            newUserFatoraPayment.SetPaymentIdAndUrlAndTriggerUrl(createPaymentResponseData.PaymentId,
                createPaymentResponseData.Url, triggerUrl);

            return createPaymentResponseData.Url;
        }
        catch (FatoraException e)
        {
            Logger.LogError(e, "Error creating payment");
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentCreationFailed);
        }
    }
    
    public async Task<string> PayMultipleAsync(
        List<Guid> requestIds,
        Guid userId,
        string language,
        bool savedCards,
        string? callBackUrl,
        string? notes
    )
    {
        if (requestIds is null || !requestIds.Any())
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RequestIdRequired);
        }
        
        if(requestIds.Count > 5)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.MaxRequestsExceeded);
        }

        await _userRepository.GetAsync(userId);

        var userFatoraPayments = new List<UserFatoraPayment>();
        var totalAmount = 0;
        var paymentIds = new StringBuilder();

        foreach (var requestId in requestIds)
        {
            var request = await _requestRepository.GetAsync(x => x.Id == requestId && x.CreatorId == userId);
            
            if (request.Status is not (RequestStatus.Pending or RequestStatus.Processing))
            {
                throw new BusinessException(GoTrackDomainErrorCodes.InvalidRequestStatus);
            }

            var bill = await BillManager.GetByRequestIdAsync(requestId);
            
            var amount = (int)bill.BillableAmount;
            totalAmount += amount;

            var userFatoraPayment = new UserFatoraPayment(
                GuidGenerator.Create(),
                userId,
                requestId,
                amount,
                callBackUrl,
                notes,
                uint.Parse(_fatoraOptions.TerminalId),
                PaymentStatus.Pending,
                request.Type
            );
            
            await _userFatoraPaymentRepository.InsertAsync(userFatoraPayment);
            userFatoraPayments.Add(userFatoraPayment);
            
            if (paymentIds.Length > 0)
            {
                paymentIds.Append(',');
            }

            paymentIds.Append(userFatoraPayment.Id);
        }

        var masterPaymentId = GuidGenerator.Create();
        
        var baseUrl = _baseUrlProvider.GetBaseUrl();
        var api = "/api/app/payment/processMultiplePayments/";
        var triggerUrl = baseUrl + api + masterPaymentId + "?userFatoraPaymentIds=" + paymentIds.ToString();

        var createPaymentRequestDto = new CreatePaymentRequestDto(
            ConvertStringToFatoraLanguage(language), 
            totalAmount,
            callBackUrl, 
            triggerUrl, 
            savedCards, 
            userId, 
            "Multiple requests payment"
        );

        var createPaymentResponseData = await _fatoraService.CreatePaymentAsync(createPaymentRequestDto);

        foreach (var payment in userFatoraPayments)
        {
            payment.SetPaymentIdAndUrlAndTriggerUrl(createPaymentResponseData.PaymentId, createPaymentResponseData.Url, triggerUrl);
            await _userFatoraPaymentRepository.UpdateAsync(payment);
        }
        
        return createPaymentResponseData.Url;
    }


    public async Task<UserFatoraPayment> CheckPaymentStatusAsync(Guid userFatoraPaymentId)
    {
        var userFatoraPayment = await _userFatoraPaymentRepository.GetAsync(userFatoraPaymentId);


        var getPaymentStatusResponseData = await _fatoraService.GetPaymentStatus(userFatoraPayment.PaymentId!.Value);

        if (getPaymentStatusResponseData.Status is FatoraPaymentStatus.Pending)
            return userFatoraPayment;

        if (getPaymentStatusResponseData.Status is FatoraPaymentStatus.Failed
            || getPaymentStatusResponseData.Status is FatoraPaymentStatus.Cancel)
        {
            Logger.LogWarning("Payment with id: {PaymentId} is {Status}", userFatoraPayment.PaymentId!.Value,
                getPaymentStatusResponseData.Status.ToString());

            return userFatoraPayment;
        }

        var request = await _requestRepository.GetAsync(userFatoraPayment.RequestId);

        await CallChangeStageAsync(request);

        userFatoraPayment.SetRrnAndPaymentStatus(getPaymentStatusResponseData.Rrn,
            getPaymentStatusResponseData.Status.ToString(), Logger);

        return userFatoraPayment;
    }

    public async Task ReversalPaymentAsync(Guid userId, Guid paymentId, string language)
    {
        await _userFatoraPaymentRepository.GetAsync(x => x.CreatorId == userId && x.PaymentId == paymentId);

        var reversalPaymentRequestDto =
            new ReversalPaymentRequestDto(ConvertStringToFatoraLanguage(language), paymentId);

        try
        {
            await _fatoraService.ReversalPayment(reversalPaymentRequestDto);
        }
        catch (Exception)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.ReversalPaymentFailed);
        }
    }

    private FatoraLanguage ConvertStringToFatoraLanguage(string language)
    {
        if (Enum.TryParse<FatoraLanguage>(language, true, out var result))
            return result;

        return language.ToLowerInvariant() switch
        {
            "ar" => FatoraLanguage.Arabic,
            "en" => FatoraLanguage.English,
            _ => FatoraLanguage.English,
        };
    }

    private async Task CallChangeStageAsync(Request request)
    {
        await _requestManager.ProcessPaymentAsync(request, PaymentMethod.FatoraService);
    }
}