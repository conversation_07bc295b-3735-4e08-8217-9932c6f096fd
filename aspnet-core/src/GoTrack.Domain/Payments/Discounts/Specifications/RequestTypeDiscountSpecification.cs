using System;
using System.Linq;
using GoTrack.Payments.Bills;
using GoTrack.Requests;
using Volo.Abp;

namespace GoTrack.Payments.Discounts.Specifications;

public class RequestTypeDiscountSpecification : IDiscountSpecification
{
    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.RequestType;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        if (!billPlan.DiscountSpecificationData.TryGetValue(
                DiscountDataKeys.RequestType, 
                out var billPlanRequestTypeValue) || 
            string.IsNullOrEmpty(billPlanRequestTypeValue))
        {
            return false;
        }

        if (!Enum.TryParse<RequestType>(billPlanRequestTypeValue, out var billPlanRequestType))
            return false;

        var allowedRequestTypes = criteria.SpecificationValue
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(rt => rt.Trim())
            .Where(rt => !string.IsNullOrEmpty(rt))
            .Select(Enum.Parse<RequestType>)
            .ToList();

        return allowedRequestTypes.Contains(billPlanRequestType);
    }

    public void CheckIfValidAsync(string data)
    {
        if (string.IsNullOrWhiteSpace(data))
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationRequestTypeInvalid);

        var requestTypes = data.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(rt => rt.Trim())
            .Where(rt => !string.IsNullOrEmpty(rt));

        if (requestTypes.Any(requestType => !Enum.TryParse<RequestType>(requestType, out _)))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationRequestTypeInvalid);
        }
    }
}