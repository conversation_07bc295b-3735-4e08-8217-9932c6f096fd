using GoTrack.Providers;
using System.Threading.Tasks;
using System;
using Volo.Abp;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;
using Volo.Abp.MailKit;

namespace GoTrack.Emails;

public class IdentityEmailVerificationManager : DomainService
{
    private readonly IMailKitSmtpEmailSender _emailSender;
    private readonly IIdentityEmailConfirmationService _identityEmailConfirmationService;
    private readonly IBaseUrlProvider _baseUrlProvider;
    private readonly IIdentityUserRepository _identityUserRepository;

    public IdentityEmailVerificationManager(
        IMailKitSmtpEmailSender emailSender,
        IIdentityEmailConfirmationService identityEmailConfirmationService,
        IBaseUrlProvider baseUrlProvider,
        IIdentityUserRepository identityUserRepository)
    {
        _emailSender = emailSender;
        _identityEmailConfirmationService = identityEmailConfirmationService;
        _baseUrlProvider = baseUrlProvider;
        _identityUserRepository = identityUserRepository;
    }

    public async Task SendTokenMailToVerifyEmailAsync(string email, Guid userId, string language)
    {
        var user = await _identityUserRepository.GetAsync(userId);

        if (user.Email != email)
            throw new BusinessException(GoTrackDomainErrorCodes.EmailVerificationEmailMismatch);

        if (user.EmailConfirmed)
            throw new BusinessException(GoTrackDomainErrorCodes.EmailVerificationAlreadyConfirmed);

        var token = await _identityEmailConfirmationService.GenerateEmailConfirmationTokenWithUserIdBase64UrlAsync(userId);

        var baseUrl = _baseUrlProvider.GetBaseUrl();

        var api = "/api/app/emailVerification/confirmIdentityEmail?token=";

        var languagePart = $"&language={language}";

        var triggerUrl = baseUrl + api + token + languagePart;

        await _emailSender.SendAsync(
            to: email,
            subject: "Email Confirmation",
            body: EmailTemplates.GetEmailConfirmationBody(triggerUrl, language),
            isBodyHtml: true
        );
    }

    public async Task ConfirmEmailAsync(string token, string language)
    {
        var email = await _identityEmailConfirmationService.ConfirmIdentityEmailAsync(token);

        await _emailSender.SendAsync(
            to: email,
            subject: "Email Successfully Confirmed",
            body: EmailTemplates.GetEmailSuccessfullyConfirmedBody(language),
            isBodyHtml: true
        );
    }
}
