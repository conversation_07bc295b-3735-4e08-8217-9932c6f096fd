using GoTrack.Routes.RouteViewModels;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.TripTemplates.TripTemplateViewModels;

public class TripTemplateViewModel : FullAuditedEntity<Guid>
{
    public string Name { get; private set; }

    public Guid TrackAccountId { get; private set; }
    
    public List<RouteViewModel> Routes { get; private set; } = new();
    public List<Vehicle> Vehicles { get; private set; } = new();
    public List<VehicleGroup> VehicleGroups { get; private set; } = new();

    public TripTemplateViewModel(
        Guid id,
        Guid? creatorId,
        DateTime creationTime,
        Guid? deleterId,
        DateTime? deletionTime,
        bool isDeleted,
        Guid? lastModifierId,
        DateTime? lastModificationTime,
        
        string name,
        Guid trackAccountId,
        List<RouteViewModel> routes,
        List<Vehicle> vehicles,
        List<VehicleGroup> vehicleGroups) 
        : base(id)
    {
        CreatorId = creatorId;
        CreationTime = creationTime;
        DeleterId = deleterId;
        DeletionTime = deletionTime;
        IsDeleted = isDeleted;
        LastModifierId = lastModifierId;
        LastModificationTime = lastModificationTime;

        Name = name;
        TrackAccountId = trackAccountId;
        Routes = routes;
        Vehicles = vehicles;
        VehicleGroups = vehicleGroups;
    }

    public static TripTemplateViewModel GetTripTemplateViewModelFromTripTemplate(
        TripTemplate tripTemplate,
        List<RouteViewModel> routes,
        List<Vehicle> vehicles,
        List<VehicleGroup> vehicleGroups)
    {
        return new TripTemplateViewModel
        (
            tripTemplate.Id,
            tripTemplate.CreatorId,
            tripTemplate.CreationTime,
            tripTemplate.DeleterId,
            tripTemplate.DeletionTime,
            tripTemplate.IsDeleted,
            tripTemplate.LastModifierId,
            tripTemplate.LastModificationTime,

            tripTemplate.Name,
            tripTemplate.TrackAccountId,
            routes,
            vehicles,
            vehicleGroups
        );
    }

}
