using System;
using System.Collections.Generic;

namespace GoTrack.AlertDefinitions.DisassembleTrackingDevices;

public class DisassembleTrackingDeviceAlertDefinition : AlertDefinition
{
    
    private DisassembleTrackingDeviceAlertDefinition() : base() { }

    internal DisassembleTrackingDeviceAlertDefinition(
        Guid id,
        Guid trackAccountId,
        List<AlertDefinitionNotificationMethod> notificationMethods
    ) : base(id, trackAccountId, notificationMethods, AlertType.DisassembleTrackingDevice)
    {
        
    }
}