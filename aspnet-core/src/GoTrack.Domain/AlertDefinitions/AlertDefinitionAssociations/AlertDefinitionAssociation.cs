using System;
using GoTrack.TrackableEntities;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.AlertDefinitions.AlertDefinitionAssociations;

public class AlertDefinitionAssociation : FullAuditedAggregateRoot<Guid>, IHaveTrackableEntity
{
    public Guid AlertDefinitionId { get; private set; }

    public Guid TrackableEntityAssociationId { get; private set; }

    #region Navigation
    
    public TrackableEntityAssociation TrackableEntityAssociation { get; private set; }

    #endregion

    private AlertDefinitionAssociation()
    {
    }

    public AlertDefinitionAssociation(
        Guid id,
        Guid alertDefinitionId,
        Guid trackableEntityAssociationId) : base(id)
    {
        AlertDefinitionId = alertDefinitionId;
        TrackableEntityAssociationId = trackableEntityAssociationId;
    }
}
