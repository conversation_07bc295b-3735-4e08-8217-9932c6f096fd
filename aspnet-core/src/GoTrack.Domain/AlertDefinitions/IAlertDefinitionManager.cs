using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.AlertDefinitions;

public interface IAlertDefinitionManager : IDomainService
{
    Task<EnteringZoneAlertDefinition> CreateEnteringZoneAlertDefinitionAsync(List<Guid> geoZoneIds, List<AlertDefinitionNotificationMethod> notificationMethods, Guid trackAccountId);
    Task<ExceedingSpeedAlertDefinition> CreateExceedingSpeedAlertDefinitionAsync(decimal maxSpeed, List<AlertDefinitionNotificationMethod> notificationMethods, Guid trackAccountId);
    Task<ExitingZoneAlertDefinition> CreateExitingZoneAlertDefinitionAsync(List<Guid> geoZoneIds, List<AlertDefinitionNotificationMethod> notificationMethods, Guid trackAccountId);
    Task<JobTimeAlertDefinition> CreateJobTimeAlertDefinitionAsync(string name, TimeOnly startTime, TimeOnly endTime, List<DayOfWeek> daysOfWeek, List<AlertDefinitionNotificationMethod> notificationMethods, Guid trackAccountId);
    Task EnabledAlertAsync(Guid alertDefinitionId);
    Task DisabledAlertAsync(Guid alertDefinitionId);
    Task RemoveAsync(Guid alertDefinitionId);
}
