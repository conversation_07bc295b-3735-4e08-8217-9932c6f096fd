using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.DisassembleTrackingDevices;
using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.AlertDefinitions.RouteAlertDefinitions.RouteAlertRoutes;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.GeoZones;
using GoTrack.Routes;
using GoTrack.TrackableEntities;
using GoTrack.TrackAccounts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions;

public class ExitingRouteAlertDefinitionManager : AlertDefinitionManager
{
    private readonly IRepository<Route, Guid> _routeRepository;

    public ExitingRouteAlertDefinitionManager(
        IRepository<Route, Guid> routeRepository,
        IRepository<EnteringZoneAlertDefinition, Guid> enteringZoneAlertDefinitionsRepository,
        IRepository<ExceedingSpeedAlertDefinition, Guid> exceedingSpeedAlertDefinitionRepository,
        IRepository<ExitingZoneAlertDefinition, Guid> exitingZoneAlertDefinitionRepository,
        IRepository<GeoZone, Guid> geoZoneRepository,
        IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<JobTimeAlertDefinition, Guid> jobTimeAlertDefinitionRepository,
        IRepository<AlertDefinition, Guid> alertRepository,
        IRepository<DisassembleTrackingDeviceAlertDefinition, Guid> disassembleTrackingDeviceAlertDefinitionRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<TrackableEntityAssociation, Guid> trackableEntityAssociationRepository,
        IAlertTriggerManagerResolver alertTriggerManagerResolver)
        : base(enteringZoneAlertDefinitionsRepository,
            exceedingSpeedAlertDefinitionRepository,
            exitingZoneAlertDefinitionRepository,
            geoZoneRepository,
            trackAccountRepository,
            jobTimeAlertDefinitionRepository,
            alertRepository,
            disassembleTrackingDeviceAlertDefinitionRepository,
            alertDefinitionAssociationRepository,
            trackableEntityAssociationRepository,
            alertTriggerManagerResolver
        )
    {
        _routeRepository = routeRepository;
    }


    public async Task<ExitingRouteAlertDefinition> CreateAsync(
        List<Guid> routeIds,
        List<AlertDefinitionNotificationMethod> notificationMethods,
        Guid trackAccountId,
        List<Guid> vehicleIds,
        List<Guid> vehicleGroupIds)
    {
        var allRoutesIds = routeIds.ToHashSet();
        var existingRoutes = await _routeRepository.GetListAsync(x => allRoutesIds.Contains(x.Id));
        if (existingRoutes.Count != allRoutesIds.Count)
            throw new EntityNotFoundException(typeof(Route));

        var alertDefinitionId = GuidGenerator.Create();

        var newRouteAlertRoutes = new List<RouteAlertRoute>();
        foreach (var routeId in allRoutesIds)
        {
            newRouteAlertRoutes.Add(
                new RouteAlertRoute(GuidGenerator.Create(), alertDefinitionId, routeId)
            );
        }

        var newExitingRouteAlertDefinition = new ExitingRouteAlertDefinition(
            alertDefinitionId,
            newRouteAlertRoutes,
            trackAccountId,
            notificationMethods
        );

        await base.CreateAsync(newExitingRouteAlertDefinition, notificationMethods, trackAccountId, vehicleIds, vehicleGroupIds);

        return newExitingRouteAlertDefinition;
    }
}
