using System;
using System.Collections.Generic;
using FirebaseAdmin.Messaging;
using GoTrack.SeedWork;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;

namespace GoTrack.Devices;

public class Device : FullAuditedAggregateRoot<Guid>
{
    public LocalizedString Model { get; private set; }
    public LocalizedString Brand { get; private set; }
    public string Imei { get; private set; }
    public string Sim { get; private set; }
    public Protocol Protocol { get; private set; }

    public Guid? OwnerId { get; private set; }

    public DeviceStatus Status { get; private set; }

    #region Navigation

    public IReadOnlyCollection<DeviceStatusLog> StatusLogs => _statusLogs.AsReadOnly();
    private readonly List<DeviceStatusLog> _statusLogs = []; 

    public IdentityUser Owner { get; private set; }

    #endregion

    private Device()
    {
    }

    internal Device(Guid id, LocalizedString model, LocalizedString brand, string imei, string sim, Protocol protocol, DeviceStatus currentStatus) : base(id)
    {
        Model = model;
        Brand = brand;
        Imei = imei;
        Sim = sim;
        Protocol = protocol;
        Status = currentStatus;
    }

    public void SetOwnerId(Guid ownerId)
    {
        if (OwnerId is not null)
            throw new BusinessException(GoTrackDomainErrorCodes.OwnerIDHasAlreadyBeenSet);

        OwnerId = ownerId;
    }

    internal void Deactivate()
    {
        if (Status != DeviceStatus.ConnectedAndActive)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DeviceCannotBeDeactivated);
        }
        Status = DeviceStatus.ConnectedAndDeactive;
    }
    
    internal void Active()
    {
        if (Status != DeviceStatus.Unconnected)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DeviceCannotBeActivated);
        }
        Status = DeviceStatus.ConnectedAndActive;
    }

}