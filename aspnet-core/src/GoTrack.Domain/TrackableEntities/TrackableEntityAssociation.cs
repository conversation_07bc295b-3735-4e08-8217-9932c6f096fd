using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace GoTrack.TrackableEntities;

public abstract class TrackableEntityAssociation : CreationAuditedEntity<Guid>
{
    public TrackableEntityType EntityType { get; private set; }

    protected TrackableEntityAssociation() { }

    public TrackableEntityAssociation(Guid id, TrackableEntityType entityType) : base(id)
    {
        EntityType = entityType;
    }
}
