using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using GoTrack.Devices;
using GoTrack.Vehicles.LicensePlates;
using Volo.Abp;
using Volo.Abp.Domain.Values;

namespace GoTrack.Requests.AccountSubscriptionRequests;

public class SubscriptionVehicleInfo : ValueObject
{
    public Color Color { get; set; }
    public SubscriptionVehicleLicensePlate LicensePlate { get; private set; }
    public double ConsumptionRate { get; private set; }
    public bool NeedsTrackingDevice { get; private set; }
    public Guid? DeviceId { get; private set; } 
    

    //navigation prop
    public Device Device { get; private set; }
    private SubscriptionVehicleInfo()
    {
    }

    public SubscriptionVehicleInfo(SubscriptionVehicleLicensePlate licensePlate, Color color, double consumptionRate, bool needsTrackingDevice)
    {
        LicensePlate = licensePlate;
        Color = color;
        ConsumptionRate = consumptionRate;
        NeedsTrackingDevice = needsTrackingDevice;
    }

    public void SetDeviceId(Guid deviceId)
    {
        DeviceId = deviceId;
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Color;
        yield return LicensePlate;
        yield return DeviceId;
        yield return ConsumptionRate;
    }

    public static List<SubscriptionVehicleInfo> CheckTrackVehiclesForLicensePlateDuplicates(
        List<SubscriptionVehicleInfo> trackVehicles)
    {
        var seen = new HashSet<(string, VehicleLicensePlateSubClass)>();

        if (trackVehicles.Select(trackVehicle =>
                (trackVehicle.LicensePlate.Serial, trackVehicle.LicensePlate.SubClass))
            .Any(key => !seen.Add(key)))
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateLicensePlate);

        return trackVehicles;
    }
}