using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Requests.RenewTrackAccountSubscriptions;

public interface IRenewSubscriptionRequestManager : IBillableRequestManager<RenewSubscriptionRequest>
{
    Task<RenewSubscriptionRequest> CreateAsync(Guid ownerId, Guid trackAccountId,
        string subscriptionPlanKey,
        int userCount,
        Guid? smsBundleId,
        List<SubscriptionVehicleInfo>? newVehicles,
        List<Guid>? removeVehicles, List<Guid> removeUsers,
        TrackerInstallationLocation? trackerInstallationLocation,
        int subscriptionDurationInMonths, string? promoCode);

    Task AcceptRequest(Guid id, string? note, decimal? discountRate);
    Task FinishProcessingAsync(Guid id, string? note);
    Task InstallDevicesAsync(Guid requestId, List<DeviceInstallationRequest> deviceRequests);
    Task ActivateSubscriptionAsync(Guid subscriptionId);
}