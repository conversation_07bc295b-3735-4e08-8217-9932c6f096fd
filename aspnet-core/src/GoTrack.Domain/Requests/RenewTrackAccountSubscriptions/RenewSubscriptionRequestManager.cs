using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Devices;
using GoTrack.Localization;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PromoCodes;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using GoTrack.Vehicles.LicensePlates;
using Microsoft.Extensions.Localization;
using Volo.Abp;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;
using Volo.Abp.Uow;

namespace GoTrack.Requests.RenewTrackAccountSubscriptions;

public class RenewSubscriptionRequestManager : DomainService, IRenewSubscriptionRequestManager
{
    private readonly IRepository<RenewSubscriptionRequest, Guid> _renewTrackAccountSubscriptionRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    private readonly IRepository<Device, Guid> _deviceRepository;
    private readonly IDeviceManager _deviceManager;
    private readonly TrackAccountSubscriptionManager _trackAccountSubscriptionManager;

    protected BillManager BillManager => LazyServiceProvider.LazyGetRequiredService<BillManager>();
    protected DiscountManager DiscountManager => LazyServiceProvider.LazyGetRequiredService<DiscountManager>();

    protected IRepository<Bill, Guid> BillRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Bill, Guid>>();

    protected IUnitOfWorkManager UnitOfWorkManager => LazyServiceProvider.LazyGetRequiredService<IUnitOfWorkManager>();

    protected IIdentityUserRepository IdentityUserRepository =>
        LazyServiceProvider.LazyGetRequiredService<IIdentityUserRepository>();

    protected RenewSubscriptionRequestBillPlanFactory RenewSubscriptionRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<RenewSubscriptionRequestBillPlanFactory>();

    protected IRepository<Vehicle, Guid> VehicleRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Vehicle, Guid>>();

    protected IRepository<UserTrackAccountAssociation, Guid> UserTrackAccountAssociationRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<UserTrackAccountAssociation, Guid>>();

    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();

    protected IStringLocalizer<GoTrackResource> Localizer =>
        LazyServiceProvider.LazyGetRequiredService<IStringLocalizer<GoTrackResource>>();

    protected VehicleCountCalculator VehicleCountCalculator
        => LazyServiceProvider.LazyGetRequiredService<VehicleCountCalculator>();


    protected SubscriptionPlanDefinitionStore SubscriptionPlanDefinitionStore =>
        LazyServiceProvider.LazyGetRequiredService<SubscriptionPlanDefinitionStore>();

    protected IGlobalVehicleUniquenessService GlobalVehicleUniquenessService =>
        LazyServiceProvider.LazyGetRequiredService<IGlobalVehicleUniquenessService>();

    protected IVehicleManager VehicleManager =>
        LazyServiceProvider.LazyGetRequiredService<IVehicleManager>();

    protected IVehicleDeviceEventLogManager DeviceEventLogManager
        => LazyServiceProvider.LazyGetRequiredService<IVehicleDeviceEventLogManager>();

    private IAbpDistributedLock _distributedLock
        => LazyServiceProvider.LazyGetRequiredService<IAbpDistributedLock>();

    private UserTrackAccountAssociationManager UserTrackAccountAssociationManager
        => LazyServiceProvider.LazyGetRequiredService<UserTrackAccountAssociationManager>();
   
    public RenewSubscriptionRequestManager(
        IRepository<RenewSubscriptionRequest, Guid> renewTrackAccountSubscriptionRepository,
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository,
        IRepository<Device, Guid> deviceRepository,
        TrackAccountSubscriptionManager trackAccountSubscriptionManager, IDeviceManager deviceManager)
    {
        _renewTrackAccountSubscriptionRepository = renewTrackAccountSubscriptionRepository;
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _deviceRepository = deviceRepository;
        _trackAccountSubscriptionManager = trackAccountSubscriptionManager;
        _deviceManager = deviceManager;
    }

    public async Task<RenewSubscriptionRequest> CreateAsync(
        Guid ownerId,
        Guid trackAccountId,
        string subscriptionPlanKey,
        int userCount,
        Guid? smsBundleId,
        List<SubscriptionVehicleInfo>? newVehicles,
        List<Guid>? removeVehicles,
        List<Guid>? removeUsers,
        TrackerInstallationLocation? trackerInstallationLocation,
        int subscriptionDurationInMonths,
        string? promoCode)
    {
        await using var handle = await _distributedLock.TryAcquireAsync($"CreatingRenewSubscriptionRequestFor{ownerId}");
        if (handle is null)
            throw new BusinessException(GoTrackDomainErrorCodes.SubscriptionRequestUnderReview);
        
        var existingRenewSubscription = await _renewTrackAccountSubscriptionRepository
            .AnyAsync(subscription =>
                subscription.RenewSubscriptionRequestStage == RenewSubscriptionRequestStage.Review
                && subscription.Status != RequestStatus.Canceled
            );

        if (existingRenewSubscription)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.SubscriptionRequestUnderReview);
        }

        if (!string.IsNullOrEmpty(promoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(promoCode);
        }

        var trackAccountSubscription = (await _trackAccountSubscriptionRepository.GetQueryableAsync())
            .Where(subscription => subscription.TrackAccountId == trackAccountId)
            .OrderByDescending(subscription => subscription.CreationTime)
            .FirstOrDefault() ?? throw new EntityNotFoundException(typeof(TrackAccountSubscription));

        if (trackAccountSubscription.State is TrackAccountSubscriptionState.Pending)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.SubscriptionStillPending);
        }

        if (newVehicles?.Count > 0 && trackerInstallationLocation is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.TrackerInstallationLocationRequired);
        }

        if (trackerInstallationLocation is not null && newVehicles?.Count is 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.NewVehiclesRequired);
        }

        if (newVehicles is not null)
        {
            var licensePlates = newVehicles.Select(x =>
                new LicensePlate(x.LicensePlate.SubClass, x.LicensePlate.Serial)).ToList();

            await GlobalVehicleUniquenessService.ValidateVehicleUniquenessAsync(licensePlates);
        }

        var subscriptionPlanDefinition = await SubscriptionPlanDefinitionStore.GetWithValidate(subscriptionPlanKey);

        if (subscriptionPlanDefinition.UserCount < userCount - (removeUsers?.Count ?? 0))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.UserRemovalListRequired);
        }

        if (removeUsers.IsNullOrEmpty() is false)
        {
            var userAssociations = await UserTrackAccountAssociationRepository.GetListAsync(x =>
                removeUsers!.Contains(x.Id) && x.AssociationType == AssociationType.Observer
            );

            if (removeUsers is not null && userAssociations.Count != removeUsers.Count)
                throw new BusinessException(GoTrackDomainErrorCodes.UserRemovalListRequired);
        }
        
        var renewTrackAccountSubscription = new RenewSubscriptionRequest(
            GuidGenerator.Create(),
            ownerId,
            trackAccountId,
            subscriptionPlanDefinition,
            userCount,
            trackAccountSubscription.Id,
            promoCode,
            smsBundleId,
            newVehicles,
            removeVehicles,
            removeUsers,
            trackerInstallationLocation,
            subscriptionDurationInMonths
        );

        var billId = await BillManager.CreateBillAsync(await GenerateBillPlan(renewTrackAccountSubscription));
        renewTrackAccountSubscription.SetBillId(billId);

        await _renewTrackAccountSubscriptionRepository.InsertAsync(renewTrackAccountSubscription, true);

        return renewTrackAccountSubscription;
    }


    public async Task AcceptRequest(Guid id, string? note, decimal? discountRate)
    {
        var request = await _renewTrackAccountSubscriptionRepository.GetAsync(id);

        if (note != null)
            request.AddNote(new RequestNote(GuidGenerator.Create(), request.Id, note));

        await UnitOfWorkManager.Current!.SaveChangesAsync();

        if (request.BillId is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillNotAssigned);
        }

        var bill = await BillRepository.GetAsync(request.BillId.Value);
        if (discountRate is not null)
        {
            await DiscountManager.ApplyDiscountOnSpecificRequest(request, bill, discountRate.Value, true);
        }

        bill.Approve();

        await BillRepository.UpdateAsync(bill, true);

        request.StartProcessing();
        request.SetStageAsPayment();

        await _renewTrackAccountSubscriptionRepository.UpdateAsync(request);
    }

    public async Task InstallDevicesAsync(Guid requestId, List<DeviceInstallationRequest> deviceRequests)
    {
        var request = await _renewTrackAccountSubscriptionRepository.GetAsync(requestId);
        if (deviceRequests.Count is 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DeviceRequestsRequired);
        }

        var distinctDeviceRequests = deviceRequests
            .GroupBy(v => new { v.Serial, v.LicensePlateSubClass })
            .Select(g => g.First())
            .ToList();

        if (distinctDeviceRequests.Count != deviceRequests.Count)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateEntriesFoundForLicensePlateSerialAndSubClass);
        }

        if (distinctDeviceRequests.Count != request.NewTrackVehicles.Count)
        {
            throw new BusinessException(GoTrackDomainErrorCodes
                .TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest);
        }

        request.SetStageAsDeviceInstallation();

        foreach (var deviceRequest in deviceRequests)
        {
            var device = await _deviceRepository.GetAsync(deviceRequest.DeviceId);

            var matchingVehicle = request.NewTrackVehicles.SingleOrDefault(subscriptionVehicle =>
                subscriptionVehicle.LicensePlate.Serial == deviceRequest.Serial &&
                subscriptionVehicle.LicensePlate.SubClass == deviceRequest.LicensePlateSubClass
            );

            if (matchingVehicle is null)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.SomeLicensePlateDoNotMatchWithRequest);
            }

            if (matchingVehicle.NeedsTrackingDevice)
            {
                if (device.OwnerId is not null)
                {
                    var message = Localizer[
                        GoTrackDomainErrorCodes.DeviceWithImeiAlreadyOwned,
                        device.Imei
                    ];

                    throw new BusinessException(code: GoTrackDomainErrorCodes.DeviceWithImeiAlreadyOwned,
                        message: message);
                }

                if (await _deviceManager.CheckDeviceAvailabilityAsync(device.Id) is false)
                {
                    var message = Localizer[
                        GoTrackDomainErrorCodes.TheDeviceStatusWithImeiMustBeInoperative,
                        device.Imei
                    ];

                    throw new BusinessException(code: GoTrackDomainErrorCodes.TheDeviceStatusWithImeiMustBeInoperative,
                        message: message);
                }
            }

            if (matchingVehicle.NeedsTrackingDevice is false)
            {
                if (device.OwnerId != request.OwnerId)
                {
                    var message = Localizer[
                        GoTrackDomainErrorCodes.TrackingDeviceWithImeiOwnerMismatch,
                        device.Imei
                    ];

                    throw new BusinessException(code: GoTrackDomainErrorCodes.TrackingDeviceWithImeiOwnerMismatch,
                        message: message);
                }

                if (device.Status != DeviceStatus.ConnectedAndDeactive)
                {
                    var message = Localizer[
                        GoTrackDomainErrorCodes.DeviceStatusWithImeiInvalidForNonTracking,
                        device.Imei
                    ];

                    throw new BusinessException(code: GoTrackDomainErrorCodes.DeviceStatusWithImeiInvalidForNonTracking,
                        message: message);
                }
            }

            matchingVehicle.SetDeviceId(deviceRequest.DeviceId);

            if (matchingVehicle.NeedsTrackingDevice)
            {
                device.SetOwnerId(request.OwnerId);
                await _deviceRepository.UpdateAsync(device);
            }
        }

        await _renewTrackAccountSubscriptionRepository.UpdateAsync(request);
    }

    public async Task FinishProcessingAsync(Guid id, string? note)
    {
        var renewSubscriptionRequest = await _renewTrackAccountSubscriptionRepository.GetAsync(id);

        if (!string.IsNullOrEmpty(note))
        {
            renewSubscriptionRequest.AddNote(new RequestNote(
                GuidGenerator.Create(),
                renewSubscriptionRequest.Id,
                note
            ));
        }

        var lastSubscription = (await _trackAccountSubscriptionRepository.GetQueryableAsync())
            .Where(subscription =>
                subscription.TrackAccountId == renewSubscriptionRequest.TrackAccountId
                && subscription.State == TrackAccountSubscriptionState.Active)
            .OrderByDescending(subscription => subscription.CreationTime)
            .FirstOrDefault();

        var newState = lastSubscription is null
            ? TrackAccountSubscriptionState.Active
            : TrackAccountSubscriptionState.Pending;

        var isLastSubscriptionActive = lastSubscription?.State == TrackAccountSubscriptionState.Active;

        var startDate = (lastSubscription != null && isLastSubscriptionActive)
            ? lastSubscription.To.AddDays(1)
            : DateTime.UtcNow;

        var newSubscription =
            await CreateTrackAccountSubscriptionAsync(renewSubscriptionRequest, TrackAccountSubscriptionState.Pending,
                startDate);
        
        renewSubscriptionRequest.SetCreatedTrackAccountSubscription(newSubscription.Id);

        var subscriptionPlanDefinition = SubscriptionPlanDefinitionStore.Get(newSubscription.SubscriptionPlanKey);

        await _trackAccountSubscriptionManager.AddFeatureBasedOnPlanAsync(
            newSubscription.Id,
            subscriptionPlanDefinition
        );

        renewSubscriptionRequest.FinishProcessing();
        renewSubscriptionRequest.SetStageAsFinish();
        
        if (newState == TrackAccountSubscriptionState.Active)
        {
            await ActivateSubscriptionAsync(newSubscription.Id);
        }
        
        await _renewTrackAccountSubscriptionRepository.UpdateAsync(renewSubscriptionRequest);
    }

    private async Task<TrackAccountSubscription> CreateTrackAccountSubscriptionAsync(
        RenewSubscriptionRequest subscriptionRequest,
        TrackAccountSubscriptionState state,
        DateTime startDate)
    {
        var subscriptionPlanDefinition = SubscriptionPlanDefinitionStore.Get(subscriptionRequest.SubscriptionPlanKey);

        var newSubscription = new TrackAccountSubscription(
            GuidGenerator.Create(),
            subscriptionRequest.TrackAccountId,
            subscriptionPlanDefinition,
            subscriptionRequest.UserCount,
            subscriptionRequest.SmsBundle?.MessagesCount ?? 0,
            subscriptionRequest.SubscriptionDurationInMonths,
            state,
            Clock
        );

        newSubscription.SetDateTimeRange(Clock, startDate,
            startDate.AddMonths(subscriptionRequest.SubscriptionDurationInMonths));
        var subscription = await _trackAccountSubscriptionRepository.InsertAsync(newSubscription, true);
        return subscription;
    }

    public async Task HandlePaymentCompletionAsync(Guid requestId)
    {
        var request = await _renewTrackAccountSubscriptionRepository.GetAsync(requestId);
        request.SetStageAsPaymentReview();
    }

    public async Task<BillPlan> GenerateBillPlan(RenewSubscriptionRequest request)
    {
        var vehicleCounts = await VehicleCountCalculator.CalculateCountsAsync(
            request.NewTrackVehicles,
            request.RemoveTrackVehicles);

        var subscriptionPlanDefinition = SubscriptionPlanDefinitionStore.Get(request.SubscriptionPlanKey);

        return await RenewSubscriptionRequestBillPlanFactory.GenerateBillPlan(
            new RenewSubscriptionRequestBillPlanInput(
                request.Id,
                request.OwnerId,
                subscriptionPlanDefinition,
                request.SubscriptionDurationInMonths,
                vehicleCounts.DevicesCount,
                vehicleCounts.CurrentVehicleCount,
                vehicleCounts.NumberOfAddedCars,
                request.SmsBundleId,
                request.PromoCode
            )
        );
    }

    public async Task ActivateSubscriptionAsync(Guid subscriptionId)
    {
        var subscription = await _trackAccountSubscriptionRepository.GetAsync(subscriptionId);

        if (subscription.State != TrackAccountSubscriptionState.Pending)
            throw new BusinessException(GoTrackDomainErrorCodes.SubscriptionAlreadyActive);

        if ( Clock.Now < subscription.From)
        {
            return;
        }
        
        var renewRequestQuery = await _renewTrackAccountSubscriptionRepository.GetQueryableAsync();

        renewRequestQuery = renewRequestQuery.Where(r =>
            r.Status == RequestStatus.Processed
            && r.CreatedTrackAccountSubscriptionId == subscriptionId);

        var renewRequest =
            await AsyncExecuter.FirstOrDefaultAsync(renewRequestQuery)
            ?? throw new EntityNotFoundException(nameof(RenewSubscriptionRequest));

        subscription.SetActive();

        await _trackAccountSubscriptionRepository.UpdateAsync(subscription);

        foreach (var vehicleInfo in renewRequest.NewTrackVehicles)
        {
            if (vehicleInfo.DeviceId == null)
                throw new BusinessException(GoTrackDomainErrorCodes.DeviceRequiredForActivation);

            var vehicle = await VehicleManager.CreateAsync(
                renewRequest.TrackAccountId,
                new LicensePlate(vehicleInfo.LicensePlate.SubClass, vehicleInfo.LicensePlate.Serial),
                vehicleInfo.Color,
                vehicleInfo.ConsumptionRate
            );

            await VehicleRepository.InsertAsync(vehicle);

            if (vehicleInfo.NeedsTrackingDevice)
            {
                var device = await _deviceRepository.GetAsync(vehicleInfo.DeviceId.Value);
                await _deviceManager.SetDeviceActiveAsync(device.Id);
                await _deviceRepository.UpdateAsync(device);
            }

            await DeviceEventLogManager.CreateAsync(
                vehicle.Id,
                vehicleInfo.DeviceId.Value,
                EventName.Installed
            );
        }

        foreach (var vehicleId in renewRequest.RemoveTrackVehicles)
        {
            var vehicle = await VehicleRepository.GetAsync(vehicleId);

            var device = await DeviceEventLogManager.GetCurrentDeviceForVehicleAsync(vehicleId);

            if (device is null)
                continue;

            await _deviceManager.SetDeviceDeactiveAsync(device.Id);

            await _deviceRepository.UpdateAsync(device);
        }

        var activeUserCount =
            await UserTrackAccountAssociationRepository.CountAsync(x => 
                x.Status == UserTrackAccountAssociationStatus.Active &&
                x.TrackAccountId == renewRequest.TrackAccountId &&
                x.AssociationType == AssociationType.Observer);

        List<Guid> observeIdsToRemove = [];

        observeIdsToRemove.AddRange(renewRequest.RemoveUsers);
        
        if (subscription.UserCount < activeUserCount - renewRequest.RemoveUsers.Count)
        {
            var queryable = await UserTrackAccountAssociationRepository.GetQueryableAsync();
            queryable = queryable.Where(x => 
                                             x.Status == UserTrackAccountAssociationStatus.Active &&
                                             x.TrackAccountId == renewRequest.TrackAccountId &&
                                             x.AssociationType == AssociationType.Observer &&
                                             !renewRequest.RemoveUsers.Contains(x.Id));
            queryable = queryable.Take(activeUserCount - renewRequest.RemoveUsers.Count - subscription.UserCount);
            var userAssociations = await AsyncExecuter.ToListAsync(queryable.Select(x => x.Id));
            observeIdsToRemove.AddRange(userAssociations);
        }

        
        foreach (var id in observeIdsToRemove)
        {
            await UserTrackAccountAssociationManager.DeactivateUserAsync(id);
        }
    }
}