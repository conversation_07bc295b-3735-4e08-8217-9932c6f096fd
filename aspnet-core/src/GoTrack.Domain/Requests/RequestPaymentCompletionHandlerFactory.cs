using System;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.AddVehiclesRequests;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Requests.SmsBundleRenewalRequests;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests;

public class RequestPaymentCompletionHandlerFactory : DomainService
{
    private readonly IServiceProvider _serviceProvider;

    public RequestPaymentCompletionHandlerFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public IBillableRequestManager GetHandler(RequestType requestType) =>
        requestType switch
        {
            RequestType.BusinessAccountSubscription => _serviceProvider
                .GetRequiredService<IBusinessAccountSubscriptionRequestManager>(),
            RequestType.PersonalAccountSubscription => _serviceProvider
                .GetRequiredService<IPersonalAccountSubscriptionRequestManager>(),
            RequestType.IncreaseUserCount => _serviceProvider.GetRequiredService<IIncreaseUserCountRequestManager>(),
            RequestType.SmsBundleRenewalRequest =>
                _serviceProvider.GetRequiredService<ISmsBundleRenewalManager>(),
            RequestType.RenewSubscription => _serviceProvider.GetRequiredService<IRenewSubscriptionRequestManager>(),
            RequestType.AddVehiclesRequest => _serviceProvider.GetRequiredService<AddVehiclesRequestManager>(),
            _ => throw new NotImplementedException($"Notification type {requestType} not implemented")
        };
}