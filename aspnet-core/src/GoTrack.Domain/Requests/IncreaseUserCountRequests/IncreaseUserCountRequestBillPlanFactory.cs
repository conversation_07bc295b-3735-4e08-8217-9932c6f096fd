using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using Volo.Abp.Domain.Services;

namespace GoTrack.Requests.IncreaseUserCountRequests;

public class IncreaseUserCountRequestBillPlanFactory : DomainService,
    IBillPlanFactory<IncreaseUserCountRequestBillPlanInput>
{
    private BillingHelperService BillingHelperService =>
        LazyServiceProvider.LazyGetRequiredService<BillingHelperService>();

    public async Task<BillPlan> GenerateBillPlan(IncreaseUserCountRequestBillPlanInput input)
    {
        var billLines = await GenerateBillLineItem(
            input.UserCount,
            input.RemainingMonths
        );

        var data = GenerateSpecificationData(input.RequestId);
        return new BillPlan(input.OwnerId, billLines, data);
    }

    private async Task<List<BillLineItem>> GenerateBillLineItem(
        int userCount,
        int remainingMonths
    )
    {
        List<BillLineItem> billLineItems = [];

        billLineItems.Add(await BillingHelperService.GetLineItemAsync(PricingItemKeys.AdditionalUsers,
            userCount, remainingMonths));

        return billLineItems;
    }

    private Dictionary<string, string> GenerateSpecificationData(Guid requestId)
    {
        return new Dictionary<string, string>
        {
            [DiscountDataKeys.RequestType] = RequestType.BusinessAccountSubscription.ToString(),
            [DiscountDataKeys.RequestId] = requestId.ToString()
        };
    }
}