using GoTrack.VehicleGroups;
using System;

namespace GoTrack.Trips;

public class TripVehicleGroup : Trip
{
    public Guid VehicleGroupId { get; private set; }

    #region Navigation
    public VehicleGroup VehicleGroup { get; private set; }
    #endregion
    private TripVehicleGroup() { }

    public TripVehicleGroup(Guid id,
        Guid tripTemplateId,
        Guid vehicleGroupId
        ) : base(
            id,
            tripTemplateId,
            TripType.VehicleGroupTrip
        )
    {
        VehicleGroupId = vehicleGroupId;
    }
}
