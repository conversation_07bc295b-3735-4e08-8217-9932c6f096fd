using System.Collections.Generic;

namespace GoTrack.SubscriptionPlans;

public class SubscriptionPlanDefinitionContext : ISubscriptionPlanDefinitionContext
{
    public Dictionary<string, SubscriptionPlanDefinition> SubscriptionPlanDefinitions { get; }

    public SubscriptionPlanDefinitionContext()
    {
        SubscriptionPlanDefinitions =  new Dictionary<string, SubscriptionPlanDefinition>();
    }

    public virtual SubscriptionPlanDefinition? GetOrNull(string key)
    {
        return SubscriptionPlanDefinitions.GetValueOrDefault(key);
    }

    public virtual void Add(SubscriptionPlanDefinition definition)
    {
        SubscriptionPlanDefinitions[definition.Key] = definition;
    }
}