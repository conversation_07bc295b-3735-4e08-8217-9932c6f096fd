using System.Collections.Generic;

namespace GoTrack.Settings;

public class GoTrackSettings
{
    private const string Prefix = "GoTrack";
    
    private const string TrackAccountSubscription = $"{Prefix}.TrackAccountSubscription.";
    public static string GracePeriod = $"{TrackAccountSubscription}GracePeriod";
    public static int GracePeriodDefaultValue = 0;

    public static string NotificationDays = $"{TrackAccountSubscription}NotificationDays";
    public static List<int> NotificationDaysDefaultValue = [15, 10, 5];

    public static string SmsLowCountThreshold = $"{TrackAccountSubscription}SmsLowCountThreshold";
    public static int SmsLowCountThresholdDefaultValue = 20;
    
    
    private const string TrackAccountRequest = $"{Prefix}.TrackAccountRequest.";

    public static string PersonalRequestFatoraPayEnabled = $"{TrackAccountRequest}PersonalRequestFatoraPayEnabled";
    public static bool PersonalRequestPayEnabledDefault = true;

    public static string BusinessRequestFatoraPayEnabled = $"{TrackAccountRequest}BusinessRequestFatoraPayEnabled";
    public static bool BusinessRequestPayEnabledDefault = true;

    public static string AddVehiclesRequestFatoraPayEnabled = $"{TrackAccountRequest}AddVehiclesRequestFatoraPayEnabled";
    public static bool AddVehiclesRequestPayEnabledDefault = true;

    public static string IncreaseUserCountRequestFatoraPayEnabled = $"{TrackAccountRequest}IncreaseUserCountRequestFatoraPayEnabled";
    public static bool IncreaseUserCountRequestPayEnabledDefault = true;

    public static string RenewTrackAccountSubscriptionFatoraPayEnabled = $"{TrackAccountRequest}RenewTrackAccountSubscriptionFatoraPayEnabled";
    public static bool RenewTrackAccountSubscriptionPayEnabledDefault = true;

    public static string SmsBundleRenewalRequestFatoraPayEnabled = $"{TrackAccountRequest}SmsBundleRenewalRequestFatoraPayEnabled";
    public static bool SmsBundleRenewalRequestPayEnabledDefault = true;
}