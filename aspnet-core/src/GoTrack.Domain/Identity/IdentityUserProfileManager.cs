using System;
using System.IO;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.BlobContainers;
using GoTrack.Emails;
using GoTrack.MimeTypes;
using GoTrack.Notifications;
using Microsoft.AspNetCore.Identity;
using Volo.Abp.BlobStoring;
using Volo.Abp.Content;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;

namespace GoTrack.Identity;

public class IdentityUserProfileManager : DomainService
{
    private IRepository<IdentityUserProfile, Guid> IdentityUserProfileRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<IdentityUserProfile, Guid>>();
    private IBlobContainer<CustomerProfilePictureContainer> BlobContainer =>
        LazyServiceProvider.LazyGetRequiredService<IBlobContainer<CustomerProfilePictureContainer>>();
    private IBlobValidator BlobValidator =>
        LazyServiceProvider.LazyGetRequiredService<IBlobValidator>();
    private IdentityEmailVerificationManager IdentityEmailVerificationManager =>
        LazyServiceProvider.LazyGetRequiredService<IdentityEmailVerificationManager>();
    private UserManager<IdentityUser> UserManager =>
        LazyServiceProvider.LazyGetRequiredService<UserManager<IdentityUser>>();

    private UserLocalizationService UserLocalizationService 
        => LazyServiceProvider.LazyGetRequiredService<UserLocalizationService>();

    
    public async Task CreateAsync(IdentityUser user, Address? address)
    {
        var mobileUserProfile = new IdentityUserProfile(user, address);
        await IdentityUserProfileRepository.InsertAsync(mobileUserProfile);
    }

    public async Task UpdateAsync(Guid userId, Address? address)
    {
        var mobileUserProfile = await IdentityUserProfileRepository.GetAsync(p => p.Id == userId);
        if(address is not null)
            mobileUserProfile.ChangeAddress(address);
        await IdentityUserProfileRepository.UpdateAsync(mobileUserProfile);
    }
    
    public async Task SetProfilePicture(Guid userId,IRemoteStreamContent file)
    {
        var mobileUserProfile = await IdentityUserProfileRepository.GetAsync(p => p.Id == userId);
        var fileBytes = await file.GetStream().GetAllBytesAsync();
        BlobValidator.ValidateImage(fileBytes);
        await BlobContainer.SaveAsync(mobileUserProfile.GetProfilePictureKey(), fileBytes,true);
    }
    
    public async Task<IRemoteStreamContent?> GetProfilePicture(Guid userId)
    {
        var mobileUserProfile = await IdentityUserProfileRepository.GetAsync(p => p.Id == userId);
        var profilePicture =  await BlobContainer.GetOrNullAsync(mobileUserProfile.GetProfilePictureKey());
        return profilePicture is not null ? new RemoteStreamContent(profilePicture) : null;
    }

    public async Task UpdateEmailAsync(Guid userId, string email, string language)
    {
        var mobileUserProfile = await IdentityUserProfileRepository.GetAsync(p => p.Id == userId);

        await UserManager.SetEmailAsync(mobileUserProfile.User, email);

        await IdentityEmailVerificationManager.SendTokenMailToVerifyEmailAsync(email, mobileUserProfile.User.Id, language);
    }
    
    public async Task CreateEmailAsync(IdentityUser identityUser, string email, string language)
    {
        await UserManager.SetEmailAsync(identityUser, email);

        await IdentityEmailVerificationManager.SendTokenMailToVerifyEmailAsync(email, identityUser.Id, language);
    }

    public async virtual Task SetPreferredLanguageAsync(Guid userId, string languageCode)
    {
        var userProfile = await IdentityUserProfileRepository.GetAsync(p => p.UserId == userId);
        userProfile.SetPreferredLanguage(languageCode);
        await IdentityUserProfileRepository.UpdateAsync(userProfile);
        
        await UserLocalizationService.InvalidateUserLanguageCacheAsync(userId);
    }

    public async Task<string> GetPreferredLanguageAsync(Guid userId)
    {
        var userProfile = await IdentityUserProfileRepository.FindAsync(p => p.UserId == userId);
        return userProfile?.PreferredLanguage ?? SupportedLanguageExtensions.DefaultLanguage;
    }
}