using System.Threading.Tasks;
using GoTrack.FCMDevices;
using GoTrack.Requests;
using GoTrack.TrackAccounts;
using GoTrack.UserTrackAccountAssociations;
using Volo.Abp;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;

namespace GoTrack.Identity;

public class GoTrackIdentityUserManager : DomainService
{
    private readonly IdentityUserManager _identityUserManager;

    private RequestManager RequestManager
        => LazyServiceProvider.LazyGetRequiredService<RequestManager>();
    private UserTrackAccountAssociationManager UserTrackAccountAssociationManager
        => LazyServiceProvider.LazyGetRequiredService<UserTrackAccountAssociationManager>();
    private TrackAccountManager TrackAccountManager
        => LazyServiceProvider.LazyGetRequiredService<TrackAccountManager>();
 private FcmDeviceManager FcmDeviceManager
        => LazyServiceProvider.LazyGetRequiredService<FcmDeviceManager>();


    public GoTrackIdentityUserManager(IdentityUserManager identityUserManager)
    {
        _identityUserManager = identityUserManager;
    }

    public async Task DeleteAsync(IdentityUser user)
    {
        if (await RequestManager.IsThereActiveRequestForUser(user.Id))
            throw new BusinessException(GoTrackDomainErrorCodes.RequestsBeingProcessed);
        
        var trackAccountIds = await UserTrackAccountAssociationManager.GetTrackAccountIdsAssociatedWithUserAsOwnerAsync(user.Id);
        foreach (var trackAccountId in trackAccountIds)
        {
            await TrackAccountManager.DeactivateTrackAccountAsync(trackAccountId);
        }
        
        await FcmDeviceManager.DeactivateAllDeviceOfUserAsync(user.Id);
        
        await _identityUserManager.UpdateSecurityStampAsync(user); // TODO Invalidate all active user sessions updating the SecurityStamp DID NOT WORK 
        await _identityUserManager.UpdateAsync(user);
    }
}