using System.Collections.Generic;
using Volo.Abp.Domain.Values;

namespace GoTrack.Msisdns;

//AKA MSISDN
public class Msisdn : ValueObject
{
    public string Cc { get; } //Country Code ex +963
    public string Ndc { get; } //National Destination Code
    public string Sn { get; } //Serial Number
    internal Msisdn(string cc, string ndc, string sn)
    {
        Cc = cc;
        Ndc = ndc;
        Sn = sn;
    }

    public override string ToString()
    {
        return $"00{Cc}{Ndc.Substring(1)}{Sn}";
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Cc; 
        yield return Ndc;
        yield return Sn;
    }
}