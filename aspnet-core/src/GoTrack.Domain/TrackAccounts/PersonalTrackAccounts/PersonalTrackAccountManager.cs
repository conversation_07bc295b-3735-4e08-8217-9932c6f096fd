using System;
using System.Threading.Tasks;
using GoTrack.UserTrackAccountAssociations;
using Volo.Abp.Domain.Services;

namespace GoTrack.TrackAccounts.PersonalTrackAccounts;

public class PersonalTrackAccountManager : DomainService , IPersonalTrackAccountManager
{
    private readonly UserTrackAccountAssociationManager _accountAssociationManager;

    public PersonalTrackAccountManager(UserTrackAccountAssociationManager accountAssociationManager)
    {
        _accountAssociationManager = accountAssociationManager;
    }

    public async Task<PersonalTrackAccount> CreateAsync(string name, Guid ownerId)
    {
        var personalTrackAccount = new PersonalTrackAccount(GuidGenerator.Create(), name);
        await _accountAssociationManager.CreateOwnerAsync(personalTrackAccount.Id, ownerId);
        return personalTrackAccount;
    }
}