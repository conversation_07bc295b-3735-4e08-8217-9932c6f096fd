using JetBrains.Annotations;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using Volo.Abp.FeatureManagement;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions.SubscriptionFeatures;

public static class TrackAccountSubscriptionFeatureManagementExtension
{
    public static Task<string> GetOrNullForSubscriptionAsync(
        this IFeatureManager featureManager,
        [NotNull] string name,
        Guid subscriptionId,
        bool fallback = true)
    {
        return featureManager.GetOrNullAsync(
            name,
            TrackAccountSubscriptionFeatureValueProvider.ProviderName,
            subscriptionId.ToString(),
            fallback
        );
    }

    public static Task<List<FeatureNameValueWithGrantedProvider>> GetAllForSubscriptionAsync(
        this IFeatureManager featureManager,
        Guid subscriptionId,
        bool fallback = true)
    {
        return featureManager.GetAllWithProviderAsync(
            TrackAccountSubscriptionFeatureValueProvider.ProviderName,
            subscriptionId.ToString(),
            fallback
        );
    }

    public static Task<FeatureNameValueWithGrantedProvider> GetOrNullWithProviderForSubscriptionAsync(
        this IFeatureManager featureManager,
        [NotNull] string name,
        Guid subscriptionId,
        bool fallback = true)
    {
        return featureManager.GetOrNullWithProviderAsync(
            name,
            TrackAccountSubscriptionFeatureValueProvider.ProviderName,
            subscriptionId.ToString(),
            fallback
        );
    }

    public static Task<List<FeatureNameValueWithGrantedProvider>> GetAllWithProviderForSubscriptionAsync(
        this IFeatureManager featureManager,
        Guid subscriptionId,
        bool fallback = true)
    {
        return featureManager.GetAllWithProviderAsync(
            TrackAccountSubscriptionFeatureValueProvider.ProviderName,
            subscriptionId.ToString(),
            fallback
        );
    }

    public static Task SetForSubscriptionAsync(
        this IFeatureManager featureManager,
        Guid subscriptionId,
        string name,
        string? value,
        bool forceToSet = false)
    {
        return featureManager.SetAsync(
            name,
            value,
            TrackAccountSubscriptionFeatureValueProvider.ProviderName,
            subscriptionId.ToString(),
            forceToSet
        );
    }
}

