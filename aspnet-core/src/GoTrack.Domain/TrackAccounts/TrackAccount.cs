using System;
using System.Collections.Generic;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.UserTrackAccountAssociations;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Guids;

namespace GoTrack.TrackAccounts;

public abstract class TrackAccount : FullAuditedAggregateRoot<Guid>
{
    public TrackAccountTypes AccountType { get; private set; }
    public string Name { get; private set; }
    
    public IReadOnlyCollection<UserTrackAccountAssociation> UserTrackAccountAssociations =>
        (_userTrackAccountAssociations as List<UserTrackAccountAssociation>)?.AsReadOnly() ??
        throw new InvalidOperationException();

    private readonly IList<UserTrackAccountAssociation> _userTrackAccountAssociations;

    public IReadOnlyCollection<TrackAccountSubscription> TrackAccountSubscriptions => _trackAccountSubscriptions.AsReadOnly();

    private readonly IList<TrackAccountSubscription> _trackAccountSubscriptions = [];

    protected TrackAccount()
    {
        _userTrackAccountAssociations = [];
        _trackAccountSubscriptions = [];
    }

    protected TrackAccount(Guid id, TrackAccountTypes accountType, string name) : base(id)
    {
        AccountType = accountType;
        Name = Check.NotNullOrWhiteSpace(name, nameof(Name));
        _userTrackAccountAssociations = [];
        _trackAccountSubscriptions = [];
    }
}