namespace GoTrack;

public static class GoTrackFeatureDefinitions
{
    public const string MyAppName = "GoTrack";
    public const string LiveMonitoring = MyAppName + ".LiveMonitoring";
    public const string ShowVehiclePath = MyAppName + ".ShowVehiclePath";
    private const string Alerts = MyAppName + ".Alerts";
    public const string ExceedingSpeedAlert = Alerts + ".ExceedingSpeedAlert";
    public const string JobTimeAlert = Alerts + ".JobTimeAlert";
    public const string ExitingRouteAlert = Alerts + ".ExitingRouteAlert";
    public const string EnteringZoneAlert = Alerts + ".EnteringZoneAlert";
    public const string ExitingZoneAlert = Alerts + ".ExitingZoneAlert";
    public const string Reports = MyAppName + ".Reports";
    public const string GeographicAreaManagement = MyAppName + ".GeographicAreaManagement";
    public const string PathManagement = MyAppName + ".PathManagement";
    public const string StopPointManagement = MyAppName + ".StopPointManagement";
    public const string GroupManagement = MyAppName + ".GroupManagement";
    public const string ObserverManagement = MyAppName + ".ObserverManagement";
    public const string SendTextMessageOnEveryAlarm = MyAppName + ".SendTextMessageOnEveryAlarm";
    public const string Dashboard = MyAppName + ".Dashboard";
    public const string TechnicalSupport = MyAppName + ".TechnicalSupport";
    public const string Notifications = MyAppName + ".Notifications";
    public const string ShareTheSite = MyAppName + ".ShareTheSite";
}
