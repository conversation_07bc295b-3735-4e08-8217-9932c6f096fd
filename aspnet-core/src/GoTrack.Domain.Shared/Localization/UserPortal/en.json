{"culture": "en", "texts": {"AppName": "GoTrack Portal", "UserPortal:fuel_consumption_rate": "Fuel Consumption Rate", "UserPortal:show_history": "Show History", "UserPortal:track": "Track Vehicle", "UserPortal:vehicle_settings": "Vehicle Settings", "UserPortal:groups": "Groups", "UserPortal:lines": "Lines", "UserPortal:alerts": "<PERSON><PERSON><PERSON>", "UserPortal:observer": "Observer", "UserPortal:basic information": "Basic Information", "UserPortal:next": "Next", "UserPortal:status_text.Pending": "Your subscription request has been received successfully and it will be reviewed as soon as possible. Thank you for your patience.", "UserPortal:status_text.Processing": "Your subscription request is now being processed. We appreciate your patience and will keep you updated once available.", "UserPortal:status_text.Processed": "Your subscription request has been accepted. You can now start using the monitoring services.", "UserPortal:status_text.Rejected": "Your subscription request has been rejected. You can contact the support team for any questions.", "UserPortal:status_text.Canceled": "Your subscription request has been canceled. If you need further assistance or have questions, please contact our support team.", "UserPortal:routesBanner": "Through this feature, you can schedule a group of alerts about vehicles that you want to track information about if they are not available on the application", "UserPortal:EmptyTap": "There is no content for this tab.", "UserPortal:observersManagementDescription": "This feature allows you to manage observers responsible for your vehicles or groups, with the ability to specify the authorized individuals who can receive tracking permissions for vehicles or groups.", "UserPortal:NoAssignedVehiclesOrGroups": "No vehicles or groups are assigned to this observer.", "UserPortal:NoResults": "No results found.", "UserPortal:EditObserver": "Edit Observer Details", "UserPortal:AddObserver": "Add Observer", "UserPortal:ObserverName": "Observer Name", "UserPortal:PhoneNumber": "Mobile Phone Number", "UserPortal:BasedOnGroups": "Based on Groups", "UserPortal:No": "No", "UserPortal:Yes": "Yes", "UserPortal:SelectGroup": "Select Group", "UserPortal:SearchGroups": "Search for groups...", "UserPortal:AllGroups": "All Groups", "UserPortal:SelectVehicle": "Select Vehicle", "UserPortal:SearchVehicles": "Search for vehicles...", "UserPortal:AllVehicles": "All Vehicles", "UserPortal:AddVehicleToObserver": "Add Vehicle to Observer:", "UserPortal:AddGroupToObserver": "Add Group to Observer:", "UserPortal:AssignNewVehicleToObserver": "Assign a new vehicle to the observer", "UserPortal:AssignNewGroupToObserver": "Assign a new group to the observer", "UserPortal:DeleteObserver": "Delete observer", "UserPortal:AssignedVehiclesAndGroups": "Assigned vehicles and groups", "UserPortal:FetchError": "An error occurred while fetching data:", "UserPortal:UnknownAction": "Unknown action:", "UserPortal:GroupsTab": "Groups", "UserPortal:GroupsFeatureDescription": "With this feature, you can create a group of vehicles to easily assign or track them based on your operational system.", "UserPortal:DefinedGroups": "Defined Groups", "UserPortal:AddGroup": "Add Group", "UserPortal:ShowAssignedVehicles": "Show Assigned Vehicles", "UserPortal:NoAssignedVehicles": "No vehicles assigned to this group.", "UserPortal:AddVehicle": "Add Vehicle", "UserPortal:AssignedVehiclesTitle": "Assigned Vehicles", "UserPortal:GroupName": "Group Name", "UserPortal:FillRequiredFields": "Please fill in all required fields", "UserPortal:SaveSuccess": "Saved successfully", "UserPortal:SaveError": "Error while saving:", "UserPortal:SaveErrorMessage": "An error occurred while saving the group", "UserPortal:AddVehicleToGroup": "Add Vehicle to Group:", "UserPortal:Km": "Km", "UserPortal:L": "L", "UserPortal:observersManagement": "Observers Management", "UserPortal:ConfirmTitle": "Confirmation", "UserPortal:ConfirmMessage": "Are you sure you want to proceed with this action?", "UserPortal:SuccessCreate": "Successfully created", "UserPortal:SuccessUpdate": "Successfully update", "UserPortal:SuccessDelete": "Successfully deleted", "UserPortal:SuccessCancel": "Successfully canceled", "UserPortal:SuccessDisable": "Successfully disabled", "UserPortal:SuccessEnable": "Successfully enabled", "UserPortal:CreationTime": "Creation Time", "UserPortal:LastModificationTime": "Last Modification Time", "UserPortal:ScheduledAlarms": "Scheduled Alarms", "UserPortal:DefinedGeographicArea": "Defined Geographic Area", "UserPortal:TabDescription": "You can manage alerts and defined geographic areas through these tabs.", "UserPortal:AlertsPageDescription": "Through this page, you can schedule a set of alerts for the vehicles you want to track information about.", "UserPortal:AlertsTab": "<PERSON><PERSON><PERSON>", "UserPortal:GeographicAreasTab": "Geographic Areas", "UserPortal:SavedAlerts": "Saved <PERSON>", "UserPortal:AddAlert": "<PERSON><PERSON>", "UserPortal:AlertType": "Alert <PERSON>", "UserPortal:NotificationMethod": "Notification Method", "UserPortal:Status": "Status", "UserPortal:Active": "Active", "UserPortal:Inactive": "Inactive", "UserPortal:CreationDate": "Creation Date", "UserPortal:ShowRelatedVehicles": "Show Related Vehicles", "UserPortal:Remove": "Remove", "UserPortal:Close": "Close", "UserPortal:Alert": "<PERSON><PERSON>", "UserPortal:ViewAssignedVehicles": "View Assigned Vehicles", "UserPortal:ViewAssignedGroups": "View Assigned Groups", "UserPortal:AddNewVehicles": "Add New Vehicles", "UserPortal:AddNewGroups": "Add New Groups", "UserPortal:DeleteAlert": "Delete Alert", "UserPortal:ActivateAlert": "Activate Alert", "UserPortal:BasicInfo": "Basic Information", "UserPortal:SelectAlertType": "Select Alert Type", "UserPortal:SelectVehicles": "Select Vehicles", "UserPortal:Loading": "Loading...", "UserPortal:SearchVehicle": "Search for a vehicle...", "UserPortal:SelectGroups": "Select Groups", "UserPortal:SelectZone": "Select Zone", "UserPortal:SearchZone": "Search for a zone...", "UserPortal:Previous": "Previous", "UserPortal:Save": "Save", "UserPortal:IncreaseSpeed": "Increase Speed", "UserPortal:DecreaseSpeed": "Decrease Speed", "UserPortal:SelectedSpeed": "Selected Speed", "UserPortal:SpeedUnit": " km/h", "UserPortal:Name": "Name", "UserPortal:SelectTime": "Select Time", "UserPortal:From": "From", "UserPortal:To": "To", "UserPortal:RepeatOnDays": "Repeat work time on weekdays", "UserPortal:EnterZone": "Enter Zone", "UserPortal:ExitZone": "Exit Zone", "UserPortal:SpeedViolation": "Speed Violation", "UserPortal:WorkTime": "Work Time", "UserPortal:NotificationMobile": "Via Mobile App", "UserPortal:NotificationSms": "SMS Notification", "UserPortal:NotificationEmail": "Email Notification", "UserPortal:NotificationData": "Notification Data", "UserPortal:NotificationDescription": "Specify the way you want to be notified about the generated alert via the application", "UserPortal:UnexpectedError": "An unexpected error occurred, please try again later", "UserPortal:SuccessOperation": "Operation completed successfully", "UserPortal:ErrorOperation": "Operation failed, please try again later", "UserPortal:ErrorFetchingData": "Failed to fetch data", "UserPortal:FillAllRequiredFields": "Please fill in all required fields", "UserPortal:CREATE_ACCOUNT_PANNER": "Reason for subscribing to the tracking service", "UserPortal:business": "Business", "UserPortal:personal": "Personal", "UserPortal:What kind of vehicles": "What kind of vehicles", "UserPortal:Machine": "Machine", "UserPortal:Electric generator": "Electric generator", "UserPortal:companyName": "Company name", "UserPortal:governorate": "Governorate", "UserPortal:city": "City", "UserPortal:area": "Area", "UserPortal:street": "Street", "UserPortal:accountName": "Account name", "UserPortal:trackerInstallationLocation": "Tracker installation location", "UserPortal:subscriptionPlan": "Subscription plan", "UserPortal:viewPlans": "View plans", "UserPortal:userCount": "Observer count", "UserPortal:add new vihicle": "Add new vehicle", "UserPortal:confirm": "Confirm", "UserPortal:Cancel": "Cancel", "UserPortal:cancel": "Cancel", "UserPortal:addVehicle": "Add Vehicle", "UserPortal:licensePlateSubClass": "License Plate Subclass", "UserPortal:licensePlateSerial": "License Plate Serial", "UserPortal:consumptionRate": "Consumption Rate", "UserPortal:color": "Color", "UserPortal:save": "Save", "UserPortal:back": "Back", "UserPortal:create new subscription": "Create new subscription", "UserPortal:name": "Name", "UserPortal:account type": "Account type", "UserPortal:account creation date": "Account creation date", "UserPortal:associationType": "Association Type", "UserPortal:review subscription status": "Review subscription status", "UserPortal:creation_flow_entro": "To continue, please enter your mobile phone number. We will send a code to the entered number for purposes Verification. Please ensure that the number entered is accurate and accessible. Thank you!", "UserPortal:creation_phone_entro1": "Enter the verification code you received at ", "UserPortal:creation_phone_entro2": " to complete the process. This code guarantees The security of your account and helps us verify your identity.", "UserPortal:firstName": "First Name", "UserPortal:lastName": "Last Name", "UserPortal:email": "Email", "UserPortal:phone": "Phone", "UserPortal:verification code": "Verification Code", "UserPortal:Next": "Next", "UserPortal:Click here to resend Code": "Click here to resend code", "UserPortal:Back": "Back", "UserPortal:Didn't receive a code": "Didn't receive a code", "UserPortal:Order Type": "Order Type", "UserPortal:Order Status": "Order Status", "UserPortal:Order Date": "Order Date", "UserPortal:Hello": "Hello", "UserPortal:Order Information": "Order Information", "UserPortal:Processing": "Processing", "UserPortal:Once the order status is accepted, your vehicles will be monitored": "Once the order status is accepted, your vehicles will be monitored", "UserPortal:Company Name/Activity": "Company Name/Activity", "UserPortal:Account Type": "Account Type", "UserPortal:Company Address": "Company Address", "UserPortal:Order Submission Date": "Order Submission Date", "UserPortal:Device Installation Location": "Device Installation Location", "UserPortal:Number of Vehicles to be Tracked": "Number of Vehicles to be Tracked", "UserPortal:Vehicles to be Tracked": "Vehicles to be Tracked", "UserPortal:Vehicle": "Vehicle", "UserPortal:Plate Type": "Plate Type", "UserPortal:Plate Number": "Plate Number", "UserPortal:Vehicle Color": "Vehicle Color", "UserPortal:requestDate": "Request Date", "UserPortal:requesrType": "Requesr Type", "UserPortal:trackVehicleCount": "Tracked Vehicle Count", "UserPortal:stage": "Stage", "UserPortal:price": "Price", "UserPortal:companyAddress": "Company Address", "UserPortal:MakePayment": "MakePayment", "UserPortal:status": "Status", "UserPortal:vehicle_number": "Vehicle Number", "UserPortal:average_speed": "Average Speed", "UserPortal:State": "State", "UserPortal:addGeoZone": "Add Geozone", "UserPortal:geoZone": "Geozone list", "UserPortal:Validation_required": "This Field is required", "UserPortal:Validation_email": "This Field is an Email", "UserPortal:Validation_minimumObserversCount": "The value must be greater than the current value ", "UserPortal:add new stop point": "Add New Stop Point", "UserPortal:subscriptionDurationInMonths": "Subscription Duration", "UserPortal:trackAccountSms": "Would you like to subscribe to SMS service to receive alerts", "UserPortal:smsBundleId": "SMS Bundle ", "UserPortal:needsTrackingDevice": "Needs tracking device", "UserPortal:notes": "Notes", "UserPortal:note": "Note", "UserPortal:PriceOffer": "Price Offer", "UserPortal:ok": "OK", "UserPortal:discountType": "Discount Type", "UserPortal:appliedAt": "Applied At", "UserPortal:value": "Value", "UserPortal:discount": "Discount", "UserPortal:total": "Total", "UserPortal:Orders": "Orders", "UserPortal:account name": "Account Name", "UserPortal:creationTime": "Creation Time", "Monthly": "Monthly", "OneTime": "One Time", "all": "All", "increace Observers": "increace Observers", "sms pundle": "sms pundle", "renew subscription": "renew subscription", "increace vehicle": "increace vehicle", "UserPortal:increase Observers": "Increase Observers", "UserPortal:sms bundle": "SMS Bundle", "UserPortal:renew subscription": "Renew Subscription", "UserPortal:increase vehicle": "Increase Vehicles", "UserPortal:currentTrackAccountSubscriptionPlan": "Track Account Plan", "UserPortal:additionalUsers": "Additional Users", "UserPortal:accountInfo": "Account Info", "UserPortal:IncreaseObservers": "Increase Observers", "UserPortal:ObserversCount": "Observers Count", "UserPortal:CurrentObserversCount": "Current Observers Count", "UserPortal:pricingItemDisplayName": "Item Name", "UserPortal:unitPrice": "Unit Price", "UserPortal:quantity": "Quantity", "UserPortal:pricingType": "Pricing Type", "month": "Month", "UserPortal:discounts": "Discounts", "SP": "SP", "UserPortal:pay": "Pay", "UserPortal:RequestType": "Request Type", "UserPortal:Pending": "Pending", "UserPortal:Completed": "Completed", "UserPortal:Rejected": "Rejected", "UserPortal:Observer Increase": "Observer Increase", "UserPortal:Vehicle Increase": "Vehicle Increase", "UserPortal:Date": "Date", "UserPortal:makePayment": "Make Payment", "SmsBundleRenewal": "SMS Bundle Renewal", "UserPortal:CurrentsmsBundleCount": "Current SMS Bundle Count", "UserPortal:message": "Message", "UserPortal:smsBundle": "SMS Bundle", "UserPortal:smsBundleCount": "SMS Bundle Count", "UserPortal:Increase Vehicle": "Increase Vehicle", "UserPortal:CurrentVehicleCount": "Current Vehicle Count", "UserPortal:vehicle": "vehicle", "UserPortal:add new vihicle here": "Add new vehicle here", "UserPortal:edit": "Edit", "UserPortal:delete": "Delete", "UserPortal:distance": "Distance", "UserPortal:maxSpeed": "Max Speed", "you have": "You have", "observers": "Observers", "UserPortal:ObserversList": "Observers List", "UserPortal:NoObserversFound": "No observers found", "UserPortal:RemoveObserver": "Remove Observer", "UserPortal:SelectObserversToRemove": "Select observers to remove", "UserPortal:removedUsers": "Removed Users", "UserPortal:decreaseUsers": "you want to decrease the number of users", "UserPortal.AboutApp": "About App", "UserPortal.ContactSupport": "Contact Support Team", "UserPortal.AppLanguage": "App Language", "UserPortal.Arabic": "Arabic", "UserPortal.English": "English", "UserPortal.Logout": "Logout", "UserPortal.change account": "Change Account", "UserPortal.my requests": "My Requests", "UserPortal:Requests": "Requests", "RenewSubscription": "Renew Subscription", "AddVehiclesRequest": "Add Vehicles Request", "IncreaseUserCount": "Increase User Count", "SmsBundleRenewalRequest": "Sms Bundle Renewal Request", "EnteringZone": "Entering Zone", "ExceedingSpeed": "Exceeding Speed", "ExitingZone": "Exiting Zone", "ExitingRoute": "Exiting Route", "MobileNotification": "Mobile Notification", "Sms": "Sms", "Mailing": "Mailing", "UserPortal:showRoute": "Show Route", "UserPortal:showGeoZone": "Show GeoZone", "You are an observer not an owner": "You are an observer not an owner", "UserPortal:addVehicleToGroup": "Add Vehicle To Group", "UserPortal:vehicleCountError": "the number of valid vehicles in the excel file is not equal to the number of vehicles you entered", "UserPortal:trackersCountError": "the number of trackers in the excel file is not equal to the number of trackers you entered", "UserPortal:add manual": "Add Manually", "UserPortal:add via excel": "Add via Excel", "UserPortal:trackersCount": "Trackers Count", "UserPortal:vehicleCount": "Vehicle Count", "UserPortal:UploadExcelFile": "Upload Excel File", "UserPortal:DownloadExcelTemplate": "Download Excel Template", "UserPortal:UploadExcelFile_1": "You must download the attached template, fill out the file, and re-upload it through the designated areas.", "UserPortal:UploadExcelFile_2": "Click on the \"Upload File\" button to download the template.", "UserPortal:UploadExcelFile_3": "The number of vehicles in the file must be exactly equal to the number of vehicles required.", "UserPortal:UploadExcelFile_4": "After filling out the file, click on the \"Add File\" button and select it from its storage location on your device.", "The Vehicle Count must be": "The Vehicle Count must be", "The tracking device Count must be": "The tracking device Count must be", "UserPortal:vehicle Adding method": "Vehicle Adding method", "UserPortal:reuploadExcel": "Reupload Excel"}}