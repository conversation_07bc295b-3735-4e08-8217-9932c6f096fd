using System.Diagnostics;
using Warp10.Scripts;
using Warp10Abstraction;
using Warp10Abstraction.Models;
using Warp10Abstraction.Sensors;

namespace Warp10.Sensors
{
    public class SensorRepository : ISensorRepository
    {
        private WarpService _warpService;
        private static readonly ActivitySource MyActivitySource = new("Masar.ProfferEntitlements");

        public SensorRepository(WarpService warpService)
        {
            _warpService = warpService;
        }

        public async Task<WarpGTS> GetAsync(string imei, Sensor sensor, DateTime from, DateTime to)
        {
            using var activity = MyActivitySource.StartActivity("SensorRepository.GetAsync");

            var warpClassName = SensorWarpClassMapper.GetWarpClassName(sensor);
            var result = await BaseScripts.GetImeiHistory(_warpService, imei, from, to, warpClassName, 1000);
            return result.FirstOrDefault();
        }

        public async Task<List<WarpGTS>> GetAsync(string imei, List<Sensor> sensors, DateTime from, DateTime to)
        {
            using var activity = MyActivitySource.StartActivity("SensorRepository.GetAsync");

            var warpClassNames = SensorWarpClassMapper.GetWarpClassNames(sensors);
            var result = await BaseScripts.GetImeiHistory(_warpService, imei, from, to, warpClassNames);

            string basicFetch = @"'" + WarpHelper.ConvertDate(from) + @"' 'fromDate' STORE
                                    '" + WarpHelper.ConvertDate(to) + @"' 'toDate' STORE
                                    '" + imei + @"' 'imei' STORE
                                        <%
                                            [ $readToken '" + warpClassNames + 
                                        @"' { 'imei' $imei } $fromDate $toDate ] FETCH 'records' STORE
                                        { 'Result' true 'Data' $records } 
                                        %>
                                        <% { 'Result' false 'Exception' ERROR }  %>
                                        <% %>
                                        TRY";
            
            return WarpHelper.CheckWarpResponse(await _warpService.ExecScript<WarpResponse<WarpGTS[]>>(basicFetch)).ToList();

        }

        
        /// <exception cref="SensorNotFound">When no sensor for the requested imei is found.</exception>
        public async Task<DateTimeOffset> GetLastActivityTimeAsync(string imei, Sensor sensor = Sensor.Speed)
        {
            using var activity = MyActivitySource.StartActivity("SensorRepository.GetLastActivityTimeAsync");

            string basicFetch = @"
                                    '" + imei + @"' 'imei' STORE
                                        <%
                                            [ $readToken '" + SensorWarpClassMapper.GetWarpClassName(sensor) + 
                                @"' { 'imei' $imei }  ] FIND 'records' STORE
                                        { 'Result' true 'Data' $records } 
                                        %>
                                        <% { 'Result' false 'Exception' ERROR }  %>
                                        <% %>
                                        TRY";
            
            
            var gtsHeaders = WarpHelper.CheckWarpResponse(await _warpService.ExecScript<WarpResponse<WarpGTS[]>>(basicFetch));

            if (gtsHeaders.Length == 0)
                throw new SensorNotFound(imei, sensor);
            
            return DateTimeOffset.FromUnixTimeMilliseconds(gtsHeaders.Single().LastActivity);
        }

        public async Task<Dictionary<string, WarpGTS[]>> GetLastRecordOfImeis(List<string> imeis, List<Sensor> sensors)
        {
             return await _warpService.GetLastRecordOfImeis(imeis.ToArray(), SensorWarpClassMapper.GetWarpClassNames(sensors));
        }
    }
}