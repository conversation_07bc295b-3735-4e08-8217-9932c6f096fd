using Warp10Abstraction;
using Warp10Abstraction.Models;

namespace Warp10.Scripts
{
    public static class StatusesScripts
    {
        public static async Task<string[]> GetIgnitionedImeis(this WarpService warpService)
        {
            string script = @"[] 'res' STORE
                                <%
                                    [ $readToken 'Ignition' { 'imei' '~.*' } NOW -1 ] FETCH
                                    [ SWAP 1 mapper.eq 0 0 0 ] MAP
                                    [ SWAP [] 1 99999999 filter.bysize ] FILTER 'records' STORE
                                    $records VALUES
                                    <% 
                                        'For section' SECTION
                                        'index' STORE
                                        'value' STORE
                                        $res  
                                        $records $index GET LABELS 'imei' GET
                                        +!
                                        'res' STORE
                                    %>
                                    true FOREACH
                                    $res UNIQUE 'res' STORE
                                    { 'Result' true 'Data' $res }
                                %>
                                <% { 'Result' false 'Exception' ERROR } %>
                                <% %>
                                TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script));
        }

        public static async Task<string[]> GetMovingImeis(this WarpService warpService, int speedLimit)
        {
            string script = speedLimit + @" 'speedLimit'  STORE
                                [] 'res' STORE
                                <%
                                    [ $readToken 'speed' { 'imei' '~.*' } NOW -1 ] FETCH 'records' STORE
                                    [ SWAP $speedLimit mapper.gt 0 0 0 ] MAP
                                    [ SWAP [] 1 99999999 filter.bysize ] FILTER 'records' STORE
                                    $records VALUES
                                    <% 
                                        'For section' SECTION
                                        'index' STORE
                                        'value' STORE
                                        $res  
                                        $records $index GET LABELS 'imei' GET
                                        +!
                                        'res' STORE
                                    %>
                                    true FOREACH
                                $res UNIQUE 'res' STORE
                                { 'Result' true 'Data' $res }
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script));
        }

        public static async Task<string[]> GetOnlineImeis(this WarpService warpService, int seconds)
        {
            string script = seconds + @" 'seconds' STORE
                            [] 'res' STORE
                            <%
                                [ $readToken 'speed' { 'imei' '~.*' } NOW $seconds s ] FETCH 'records' STORE
                                $records VALUES
                                <% 
                                    'For section' SECTION
                                    'index' STORE
                                    'value' STORE
                                    $res  
                                    $records $index GET LABELS 'imei' GET
                                    +!
                                    'res' STORE
                                %>
                                true FOREACH
                                $res UNIQUE 'res' STORE
                                { 'Result' true 'Data' $res }
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script));
        }

        public static async Task<string[]> GetStoppedImeis(this WarpService warpService, int speedLimit)
        {
            string script = speedLimit + @" 'speedLimit' STORE
                            [] 'res' STORE
                            <%
                                [ $readToken 'speed' { 'imei' '~.*' } NOW -1 ] FETCH
                                [ SWAP $speedLimit mapper.lt 0 0 0 ] MAP
                                [ SWAP [] 1 99999999 filter.bysize ] FILTER 'records' STORE
                                $records VALUES
                                <% 
                                    'For section' SECTION
                                    'index' STORE
                                    'value' STORE
                                    $res  
                                    $records $index GET LABELS 'imei' GET
                                    +!
                                    'res' STORE
                                %>
                                true FOREACH
                                $res UNIQUE 'res' STORE
                                { 'Result' true 'Data' $res }
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script));
        }
    }
}
