using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Warp10
{
    public class WarpService
    {
        private WarpConfiguration Configuration { get; }
        private readonly ILogger<WarpService> _logger;

        public WarpService()
        {
            
        }

        public WarpService(string url, string readToken, string writeToken, ILogger<WarpService> logger = null)
        {
            _logger = logger;
            Configuration = new WarpConfiguration(url, readToken, writeToken);
        }
        public WarpService(string url, string readToken, string writeToken, string reportingUrl, ILogger<WarpService> logger = null)
        {
            _logger = logger;
            Configuration = new WarpConfiguration(url, readToken, writeToken, reportingUrl);
        }

        public virtual async Task<TT> ExecScript<TT>(string body, bool isHeavyReporting = false)
        {
            // Add tokens to the body
            body = @"   <%
                        " + body + @"
                        %>
                        <% 
                            ERROR 0 GET 'type' GET 'class io.warp10.script.WarpScriptStopException' !=
                            <% 
                                CLEAR
                                { 'Result' false 'Exception' ERROR } 
                            %> IFT
                        %>
                        <% %> TRY";
            
            body = "'" + Configuration.ReadToken + "' 'readToken' STORE \r\n" + body;
            body = "'" + Configuration.WriteToken + "' 'writeToken' STORE \r\n" + body;

            _logger?.LogDebug(body);
            
            var client = new HttpClient();
            if (isHeavyReporting)
                client.Timeout = TimeSpan.FromMinutes(10);

            client.BaseAddress = new Uri(isHeavyReporting? Configuration.ReportingUrl: Configuration.Url);
            client.DefaultRequestHeaders.Add("Accept", "application/json");
            client.DefaultRequestHeaders.Add("Cookie", "gs_language=english");

            var data = new StringContent(body, Encoding.UTF8, "text/plain");
            var url = isHeavyReporting ? Configuration.ReportingUrl : Configuration.Url;

            try
            {
                HttpResponseMessage response = await client.PostAsync(url, data);

                response.EnsureSuccessStatusCode();
                var resp = await response.Content.ReadAsStringAsync();

                List<TT> res = JsonConvert.DeserializeObject<List<TT>>(resp);
                if (res == null)
                {
                    return default;
                }

                return res.First();
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine("CUSTOM WRITELINE");
                Console.WriteLine(e.StatusCode);
                Console.WriteLine(e.Message);
                Console.WriteLine(e);
                Console.WriteLine(e.InnerException);
                Console.WriteLine(body);
                throw;
            }
            catch (Exception e)
            {
                Console.WriteLine("CUSTOM WRITELINE222");
                Console.WriteLine(e.Message);
                Console.WriteLine(e);
                Console.WriteLine(e.InnerException);
                Console.WriteLine(body);
                throw;
            }
        }
    }
}