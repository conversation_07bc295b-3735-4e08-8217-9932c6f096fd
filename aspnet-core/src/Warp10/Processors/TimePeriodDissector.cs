using Warp10.TimePeriods;
using Warp10Abstraction.Models;

namespace Warp10.Processors
{
    public class TimePeriodDissector : ITimePeriodDissector
    {
        
        public List<TimePeriod> Trim(List<TimePeriod> timePeriods, DateTimeOffset minFrom, DateTimeOffset maxTo)
        {
            var result = new List<TimePeriod>();
            var trimPeriod = new TimePeriod(minFrom, maxTo);

            foreach (var timePeriod in timePeriods)
            {
                var intersection = trimPeriod.GetIntersection(timePeriod);
                if (intersection is not null)
                    result.Add(intersection);
            }
            
            return result;
        }

        public List<Tuple<DateTimeOffset, WarpGTSValue>> ParseTimeStamps(WarpGTSValue[] values)
        {
            return ParseOrderAsc(values);
        }
        
        public List<TimePeriod> DissectByMinValue(List<Tuple<DateTimeOffset, WarpGTSValue>>  warpGts, decimal valueThreshold, TimeSpan asc)
        {
            List<TimePeriod> result = new List<TimePeriod>();
            if (warpGts is null )
                return result;

            if (warpGts.Count < 1)
                return result;

            // var orderedSampleList = ParseOrderAsc(warpGts.Values);
            
            
            TimePeriod currentTimePeriod = null;

            foreach (var sample in warpGts)
            {
                if (!decimal.TryParse(sample.Item2.Value, out decimal sensorValue))
                    continue;

                if (currentTimePeriod is not null)
                {
                    if (currentTimePeriod.IsToSet)
                    {
                        if (sample.Item1 - currentTimePeriod.To > asc)
                        {
                            currentTimePeriod.To += asc;
                            result.Add(currentTimePeriod.Clone());
                            currentTimePeriod = null;
                        }
                    }
                    else if (sample.Item1 - currentTimePeriod.From > asc)
                    {
                        currentTimePeriod.To = currentTimePeriod.From + asc;
                        result.Add(currentTimePeriod.Clone());
                        currentTimePeriod = null;
                    }
                }

                if (sensorValue >= valueThreshold)
                {
                    if (currentTimePeriod is null)
                    {
                        currentTimePeriod = new TimePeriod(sample.Item1 );
                    }
                    else
                    {
                        currentTimePeriod.To = sample.Item1;
                        continue;
                    }
                    
                }
                else
                {
                    if (currentTimePeriod is not null)
                    {
                        currentTimePeriod.To = sample.Item1;
                        result.Add(currentTimePeriod.Clone());
                        currentTimePeriod = null;
                    }
                    
                }
            }
            
            if (currentTimePeriod is not null)
            {
                if (currentTimePeriod.IsToSet)
                {
                    currentTimePeriod.To += asc;
                    result.Add(currentTimePeriod.Clone());
                }
                else
                {
                    currentTimePeriod.To = currentTimePeriod.From + asc;
                    result.Add(currentTimePeriod.Clone());
                }
                
            }


            return result;
        }


        public List<Tuple<DateTimeOffset, WarpGTSValue>> ParseOrderAsc(WarpGTSValue[] warpGts)
        {
            List<Tuple<DateTimeOffset, WarpGTSValue>> result = new List<Tuple<DateTimeOffset, WarpGTSValue>>();

            foreach (var sample in warpGts)
            {
                if (!long.TryParse(sample.Timestamp, out var timeStamp))
                    continue;
                result.Add(
                    new Tuple<DateTimeOffset, WarpGTSValue>(
                        DateTimeOffset.FromUnixTimeMilliseconds(timeStamp),
                        sample
                        )
                    );
            }

            result = result.OrderBy(s => s.Item1).ToList();
            
            return result;
        }

        public List<Tuple<DateTimeOffset, WarpGTSValue>> FilterOutliers(List<Tuple<DateTimeOffset, WarpGTSValue>> gtsTuple, TimeSpan maxGapFilter)
        {
            if (gtsTuple is null || gtsTuple.Count < 2)
                return gtsTuple;

            var result = new List<Tuple<DateTimeOffset, WarpGTSValue>>();
            
            for (int i = 0; i < gtsTuple.Count - 1; i++)
            {
                
                if (gtsTuple[i + 1].Item1 - gtsTuple[i].Item1 < maxGapFilter)
                {
                    result.Add(gtsTuple[i]);
                }
            }
            result.Add(gtsTuple.Last());
            
            return result;

        }
    }
}