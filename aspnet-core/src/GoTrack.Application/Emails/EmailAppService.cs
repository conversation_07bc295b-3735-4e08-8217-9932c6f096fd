using System;
using System.Threading.Tasks;
using GoTrack.Notifications.NotificationFactories.AlertNotifications;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MailKit;
using Volo.Abp.Settings;

namespace GoTrack.Emails;

public class EmailAppService : GoTrackAppService, IEmailAppService
{
    private readonly IMailKitSmtpEmailSender _emailSender;
    private readonly ISettingEncryptionService _settingEncryptionService;
    private readonly IDistributedEventBus _distributedEventBus;

    public EmailAppService(IMailKitSmtpEmailSender emailSender, ISettingEncryptionService settingEncryptionService, IDistributedEventBus distributedEventBus)
    {
        _emailSender = emailSender;
        _settingEncryptionService = settingEncryptionService;
        _distributedEventBus = distributedEventBus;
    }

    public async Task SendTestEmailAsync()
    {
        await _emailSender.SendAsync(
            to: "<EMAIL>",
            subject: "test email",
            body: "<html><body><h1>Test Email</h1><p>This is a test email.</p></body></html>",
            isBodyHtml: true
        );
    }

    public async Task<string?> GetEncrypedPasswordAsync(string password)
    {
        return _settingEncryptionService.Encrypt(new SettingDefinition("password", password, isEncrypted: true), password);
    }
}