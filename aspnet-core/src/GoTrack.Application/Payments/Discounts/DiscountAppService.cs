using GoTrack.Payments.Discounts.DTOs;
using GoTrack.Permissions;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Payments.Discounts;

public class DiscountAppService : GoTrackAppService, IDiscountAppService
{
    private readonly IRepository<Discount, Guid> _discountRepository;
    private readonly DiscountManager _discountManager;

    public DiscountAppService(
        IRepository<Discount, Guid> discountRepository,
        DiscountManager discountManager)
    {
        _discountRepository = discountRepository;
        _discountManager = discountManager;
    }

    [Authorize(GoTrackPermissions.DiscountIndex)]
    public virtual async Task<PagedResultDto<DiscountDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _discountRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var discounts = await AsyncExecuter.ToListAsync(query);

        var discountDtos =
            ObjectMapper.Map<List<Discount>, List<DiscountDto>>(discounts);

        return new PagedResultDto<DiscountDto>(totalCount, discountDtos);
    }
    
    [Authorize(GoTrackPermissions.DiscountCreate)]
    public virtual async Task CreateAsync(CreateDiscountDto input)
    {
        await _discountManager.CreateDiscountAsync(
            GuidGenerator.Create(),
            input.Name,
            input.TargetType,
            input.Value,
            input.IsPercentage,
            input.StartDate,
            input.DiscountCriteriaDtos
                .Select(x => new DiscountCriteriaViewModel
                    (x.DiscountSpecificationKey, x.DiscountSpecificationData)
                )
                .ToList(),
            input.PricingItemKeys,
            input.EndDate
        );
    }

    [Authorize(GoTrackPermissions.DiscountDelete)]
    public virtual async Task DeleteAsync(Guid id)
    {
        var discount = await _discountRepository.GetAsync(id);

        await _discountRepository.DeleteAsync(discount);
    }
}