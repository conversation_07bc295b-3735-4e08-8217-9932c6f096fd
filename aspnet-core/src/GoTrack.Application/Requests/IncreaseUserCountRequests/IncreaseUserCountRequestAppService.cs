using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Permissions;
using GoTrack.Requests.IncreaseUserCountRequests.DTOs;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Requests.IncreaseUserCountRequests;

public class IncreaseUserCountRequestAppService : GoTrackAppService, IIncreaseUserCountRequestsAppService
{
    private readonly IRepository<IncreaseUserCountRequest, Guid> _increaseUserCountRequestRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;

    public IncreaseUserCountRequestAppService(
        IRepository<IncreaseUserCountRequest, Guid> increaseUserCountRequestRepository,
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository,
        IRepository<TrackAccount, Guid> trackAccountRepository)
    {
        _increaseUserCountRequestRepository = increaseUserCountRequestRepository;
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _trackAccountRepository = trackAccountRepository;
    }

    [Authorize(GoTrackPermissions.IncreaseUserCountRequestIndex)]
    public async Task<PagedResultDto<IncreaseUserCountRequestDto>> GetListAsync(PagedResultRequestDto input)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var query = await _increaseUserCountRequestRepository.WithDetailsAsync(r => r.Owner, request => request.TrackAccountSubscription);

        var totalCount = await AsyncExecuter.CountAsync(query);
        query = query.PageBy(input);

        var requests = await AsyncExecuter.ToListAsync(query);

        var requestDtos = ObjectMapper.Map<List<IncreaseUserCountRequest>, List<IncreaseUserCountRequestDto>>(requests);

        return new PagedResultDto<IncreaseUserCountRequestDto>(totalCount, requestDtos);
    }

    [Authorize(GoTrackPermissions.IncreaseUserCountRequestDetails)]
    public async Task<IncreaseUserCountRequestDetailDto> GetAsync(Guid id)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var request = (await _increaseUserCountRequestRepository.WithDetailsAsync(r => r.Owner, countRequest => countRequest.TrackAccountSubscription))
                      .SingleOrDefault(r => r.Id == id)
                      ?? throw new EntityNotFoundException(typeof(IncreaseUserCountRequest), id);
        var trackAccountSubscription = await _trackAccountSubscriptionRepository.GetAsync(subscription =>
            subscription.Id == request.TrackAccountSubscriptionId
        );
        
        var trackAccount = await _trackAccountRepository.GetAsync(account => account.Id == trackAccountSubscription.TrackAccountId);

        var userCountRequestDetailDto = ObjectMapper.Map<IncreaseUserCountRequest, IncreaseUserCountRequestDetailDto>(request);
        userCountRequestDetailDto.TrackAccountName = trackAccount.Name;

        return userCountRequestDetailDto;
    }
}