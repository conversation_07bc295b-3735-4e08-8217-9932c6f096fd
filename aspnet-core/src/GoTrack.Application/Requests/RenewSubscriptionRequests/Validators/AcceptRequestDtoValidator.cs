using FluentValidation;
using GoTrack.Localization;
using GoTrack.Requests.RenewSubscriptionRequests.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Requests.RenewSubscriptionRequests.Validators;

public class AcceptRequestDtoValidator : AbstractValidator<AcceptRequestDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public AcceptRequestDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        // RuleFor(x => x.DiscountRate)
        //     .GreaterThan(0)
        //     .LessThanOrEqualTo(1)
        //     .WithName(_localizer["GoTrack:DiscountRate"]);
    }
}