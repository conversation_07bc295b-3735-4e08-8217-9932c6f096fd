using GoTrack.Identity;
using GoTrack.Permissions;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AddVehiclesRequests.DTOs;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Requests.RenewSubscriptionRequests.DTOs;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Requests.AddVehiclesRequests;

public class AddVehiclesRequestAppService : GoTrackAppService, IAddVehiclesRequestAppService
{
    private readonly IRepository<AddVehiclesRequest, Guid> _addVehiclesRequestRepository;
    private readonly AddVehiclesRequestManager _addVehiclesRequestManager;

    public AddVehiclesRequestAppService(
        IRepository<AddVehiclesRequest, Guid> addVehiclesRequestRepository,
        AddVehiclesRequestManager addVehiclesRequestManager)
    {
        _addVehiclesRequestRepository = addVehiclesRequestRepository;
        _addVehiclesRequestManager = addVehiclesRequestManager;
    }

    [Authorize(GoTrackPermissions.AddVehiclesRequestFinishProcessing)]
    public async Task FinishProcessingAsync(Guid id, AddVehiclesRequestsFinishProcessingDto input)
    {
        await _addVehiclesRequestManager.FinishProcessingAsync(id, input.Note);
    }

    [Authorize(GoTrackPermissions.AddVehiclesRequestDetails)]
    public async Task<AddVehiclesRequestDto> GetAsync(Guid id)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();
        
        var query = await _addVehiclesRequestRepository
            .WithDetailsAsync(request => 
                    request.Owner,
                request => request.TrackAccountSubscription,
                request => request.TrackAccountSubscription.TrackAccount);

        var addVehiclesRequest = await AsyncExecuter.FirstOrDefaultAsync(query.Where(x => x.Id == id));

        if (addVehiclesRequest is null)
        {
            throw new EntityNotFoundException(typeof(AddVehiclesRequest));
        }
        
        return ObjectMapper.Map<AddVehiclesRequest, AddVehiclesRequestDto>(addVehiclesRequest);
    }

    [Authorize(GoTrackPermissions.AddVehiclesRequestGetListVehiclesWithSearch)]
    public async Task<PagedResultDto<SubscriptionVehicleInfoSearchDto>> GetListVehiclesWithSearchAsync(Guid id,GetListNewVehiclesWithSearchDto input)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();
        
        var request = await _addVehiclesRequestRepository.GetAsync(id);

        var requestNewTrackVehicles = request.TrackVehicles;
        if (input.LicensePlateSubClass is not null)
        {
            requestNewTrackVehicles = requestNewTrackVehicles.Where(x => x.LicensePlate.SubClass == input.LicensePlateSubClass).ToList();
        }

        if (input.LicensePlateSerial is not null)
        {
            requestNewTrackVehicles = requestNewTrackVehicles.Where(x => x.LicensePlate.Serial.Contains(input.LicensePlateSerial)).ToList();
        }

        var totalCount = requestNewTrackVehicles.Count;

        var vehicleInfos = requestNewTrackVehicles.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

        var vehicleInfoDtos = ObjectMapper.Map<List<SubscriptionVehicleInfo>, List<SubscriptionVehicleInfoSearchDto>>(vehicleInfos);

        return new PagedResultDto<SubscriptionVehicleInfoSearchDto>(totalCount, vehicleInfoDtos);

    }

    [Authorize(GoTrackPermissions.AddVehiclesRequestIndex)]
    public async Task<PagedResultDto<AddVehiclesRequestDto>> GetListAsync(PagedResultRequestDto input)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var query = await _addVehiclesRequestRepository
            .WithDetailsAsync(request => 
                request.Owner,
                request => request.TrackAccountSubscription,
                request => request.TrackAccountSubscription.TrackAccount);


        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var requests = await AsyncExecuter.ToListAsync(query);

        var requestDtos = ObjectMapper.Map<List<AddVehiclesRequest>, List<AddVehiclesRequestDto>>(requests);

        return new PagedResultDto<AddVehiclesRequestDto>(totalCount, requestDtos);
    }

    [Authorize(GoTrackPermissions.AddVehiclesRequestInstallDevices)]
    public async Task InstallDevicesAsync(Guid id, AddVehiclesRequestsInstallDevicesDto input)
    {
        var duplicateDeviceIds = input.VehicleDeviceDtos
            .GroupBy(v => v.DeviceId)
            .Where(g => g.Count() > 1)
            .ToList();

        if (duplicateDeviceIds.Count is not 0)
            throw new UserFriendlyException(L[GoTrackAdminApplicationErrorCodes.DuplicateDevice]);

        var deviceRequests = input.VehicleDeviceDtos.Select(x => new DeviceInstallationRequest()
        {
            DeviceId = x.DeviceId,
            LicensePlateSubClass = x.LicensePlate.SubClass,
            Serial = x.LicensePlate.Serial
        }).ToList();

        await _addVehiclesRequestManager.InstallDevicesAsync(id, deviceRequests);
    }
}
