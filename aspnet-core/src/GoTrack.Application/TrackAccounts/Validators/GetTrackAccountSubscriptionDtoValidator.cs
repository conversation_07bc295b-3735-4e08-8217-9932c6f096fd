using FluentValidation;
using GoTrack.Localization;
using GoTrack.TrackAccounts.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.TrackAccounts.Validators;

public class GetTrackAccountSubscriptionDtoValidator : AbstractValidator<GetTrackAccountSubscriptionDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public GetTrackAccountSubscriptionDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;
    }
}