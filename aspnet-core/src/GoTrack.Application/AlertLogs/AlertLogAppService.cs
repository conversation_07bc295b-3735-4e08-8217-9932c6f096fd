using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.AlertLogs.DTOs;
using GoTrack.Alerts.AlertLogs;
using GoTrack.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.AlertLogs;

public class AlertLogAppService : GoTrackAppService, IAlertLogAppService
{
    private readonly IRepository<AlertLog, Guid> _alertLogsRepository;

    public AlertLogAppService(IRepository<AlertLog, Guid> alertLogsRepository)
    {
        _alertLogsRepository = alertLogsRepository;
    }

    [Authorize(GoTrackPermissions.AlertLogIndex)]
    public async Task<PagedResultDto<AlertLogDto>> GetListAsync(AlertLogPagedResultRequestDto input, DateTime? fromDate,
        DateTime? toDate)
    {
        var query = await _alertLogsRepository.WithDetailsAsync(log => log.Device,
            log => log.Vehicle, log => log.AlertTrigger);
        query = query.OrderBy(log => log.EndedAt).ThenBy(log => log.StartedAt);
        if (input.DeviceId is not null)
        {
            query = query.Where(log => log.Device.Id == input.DeviceId);
        }

        if (input.VehicleId is not null)
        {
            query = query.Where(log => log.Vehicle.Id == input.VehicleId);
        }
        
        if (fromDate.HasValue || toDate.HasValue)
        {
            query = query.Where(log =>
                (!fromDate.HasValue || log.StartedAt >= fromDate.Value || log.EndedAt >= fromDate.Value) &&
                (!toDate.HasValue || log.StartedAt <= toDate.Value || log.EndedAt <= toDate.Value)
            );
        }
        query = query.PageBy(input);
        var count = await AsyncExecuter.CountAsync(query);
        var alertLogs = await AsyncExecuter.ToListAsync(query);
        return new PagedResultDto<AlertLogDto>(count,
            ObjectMapper.Map<List<AlertLog>, List<AlertLogDto>>
                (alertLogs));
    }
}