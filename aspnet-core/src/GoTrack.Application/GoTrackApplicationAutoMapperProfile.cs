using System;
using System.Drawing;
using System.Globalization;
using System.Linq;
using AutoMapper;
using GoTrack.Addresses;
using GoTrack.AlertLogs.DTOs;
using GoTrack.Alerts.AlertLogs;
using GoTrack.Devices;
using GoTrack.Devices.DTOs;
using GoTrack.Localization;
using GoTrack.Payments.Discounts.DTOs;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PricingItems;
using GoTrack.Payments.PricingItems.DTOs;
using GoTrack.Payments.PromoCodes;
using GoTrack.Payments.PromoCodes.DTOs;
using GoTrack.PrivacyPolicies;
using GoTrack.PrivacyPolicies.DTOs;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.AddVehiclesRequests;
using GoTrack.Requests.AddVehiclesRequests.DTOs;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Requests.IncreaseUserCountRequests.DTOs;
using GoTrack.Requests.RenewSubscriptionRequests.DTOs;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Requests.SmsBundleRenewalRequests;
using GoTrack.Requests.SmsBundleRenewalRequests.DTOs;
using GoTrack.SmsBundles;
using GoTrack.SmsBundles.DTOs;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.BusinessTrackAccounts;
using GoTrack.TrackAccounts.DTOs;
using GoTrack.TrackAccounts.PersonalTrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.DTOs;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.Extensions.Localization;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Features;
using Volo.Abp.Localization;
using GoTrack.Vehicles;

namespace GoTrack;

public class GoTrackApplicationAutoMapperProfile : Profile
{
    public GoTrackApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */

        CreateMap<PersonalAccountSubscriptionRequest, PersonalAccountSubscriptionRequestDto>()
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage));
        CreateMap<PersonalAccountSubscriptionRequest, PersonalAccountSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.OwnerFirstName, opt => opt.MapFrom(src => src.Owner.Name))
            .ForMember(dest => dest.OwnerLastName, opt => opt.MapFrom(src => src.Owner.Surname))
            .ForMember(dest => dest.OwnerEmail, opt => opt.MapFrom(src => src.Owner.Email))
            .ForMember(dest => dest.OwnerPhoneNumber, opt => opt.MapFrom(src => src.Owner.PhoneNumber))
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );

        CreateMap<BusinessAccountSubscriptionRequest, BusinessAccountSubscriptionRequestDto>()
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage));
        CreateMap<BusinessAccountSubscriptionRequest, BusinessAccountSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.OwnerFirstName, opt => opt.MapFrom(src => src.Owner.Name))
            .ForMember(dest => dest.OwnerLastName, opt => opt.MapFrom(src => src.Owner.Surname))
            .ForMember(dest => dest.OwnerEmail, opt => opt.MapFrom(src => src.Owner.Email))
            .ForMember(dest => dest.OwnerPhoneNumber, opt => opt.MapFrom(src => src.Owner.PhoneNumber))
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );
        CreateMap<Address, AddressDto>()
            .ForMember(dest => dest.GovernorateDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.Governorate))
            .ForMember(dest => dest.CityDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.City))
            .ForMember(dest => dest.CountryDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.Country));

        CreateMap<SubscriptionVehicleInfo, SubscriptionVehicleInfoDto>()
            .ForMember(
                dest => dest.ColorHex,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color)))
            .ForMember(dest => dest.LicensePlate,
                opt => opt.MapFrom(src => String.Concat(src.LicensePlate.SubClass, "-", src.LicensePlate.Serial)));

        CreateMap<SubscriptionVehicleInfo, SubscriptionVehicleInfoSearchDto>()
            .ForMember(
                dest => dest.LicensePlateSerial,
                opt => opt.MapFrom(src => src.LicensePlate.Serial)
            )
            .ForMember(dest => dest.LicensePlateSubClass,
                opt => opt.MapFrom(src => src.LicensePlate.SubClass
                )
            );


        CreateMap<RequestNote, InstallDevicesDto>();
        CreateMap<RequestNote, RequestNoteDto>();
        CreateMap<Request, RequestDto>()
            .ForMember(dest => dest.OwnerPhoneNumber, opt => opt.MapFrom(src => src.Owner.PhoneNumber));

        CreateMap<Device, DeviceDto>()
            .ForMember(dest => dest.Brand,
                opt => opt.MapFrom(src => src.Brand.GetValue(CultureInfo.CurrentCulture.Name)))
            .ForMember(dest => dest.Model,
                opt => opt.MapFrom(src => src.Model.GetValue(CultureInfo.CurrentCulture.Name)));

        CreateMap<DeviceStatusLog, DeviceStatusDto>();
        CreateMap<Device, AvailableDevicesForInstallationDeviceDto>();
        CreateMap<Device, DeactivatedDevicesDto>();

        CreateMap<Device, DeviceDetailsDto>().ForMember(dest => dest.Brand,
                opt => opt.MapFrom(src => src.Brand.GetValue(CultureInfo.CurrentCulture.Name)))
            .ForMember(dest => dest.Model,
                opt => opt.MapFrom(src => src.Model.GetValue(CultureInfo.CurrentCulture.Name)))
            .ForMember(dest => dest.DeviceStatus,
                opt => opt.MapFrom(x => x.StatusLogs));

        CreateMap<TrackAccount, TrackAccountDto>();

        CreateMap<PersonalTrackAccount, PersonalTrackAccountDetailsDto>();
        CreateMap<BusinessTrackAccount, BusinessTrackAccountDetailsDto>();

        CreateMap<FeatureValueProviderInfo, FeatureProviderDto>();
        CreateMap<FeatureNameValueWithGrantedProvider, FeatureDto>();
        CreateMap<FeatureDefinition, FeatureDto>();
        CreateMap<AlertLog, AlertLogDto>();
        CreateMap<SmsBundle, SmsBundleDto>();
        CreateMap<UserTrackAccountAssociation, UserTrackAccountAssociationDto>();
        CreateMap<RenewSubscriptionRequest, RenewSubscriptionRequestDto>()
            .ForMember(dest => dest.NewTrackVehiclesCount, opt => opt.MapFrom(x => x.NewTrackVehicles.Count));
        CreateMap<IncreaseUserCountRequest, IncreaseUserCountRequestDetailDto>();
        CreateMap<IncreaseUserCountRequest, IncreaseUserCountRequestDto>();
        CreateMap<RenewSubscriptionRequest, RenewSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.NewTrackVehiclesCount, opt => opt.MapFrom(x => x.NewTrackVehicles.Count))
            .ForMember(dest => dest.RemoveTrackVehiclesCount, opt => opt.MapFrom(x => x.RemoveTrackVehicles.Count))
            .ForMember(dest => dest.RemoveUsersCount, opt => opt.MapFrom(x => x.RemoveUsers.Count))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );

        CreateMap<SmsBundleRenewalRequest, SmsBundleRenewalRequestDetailsDto>();
        CreateMap<SmsBundleRenewalRequest, SmsBundleRenewalRequestDto>();

        CreateMap<PricingItemDefinition, PricingItemDefinitionDto>()
            .ForMember(
                dest => dest.DisplayName,
                opt => opt.ConvertUsing<LocalizationResolver, ILocalizableString>(src => src.DisplayName)
            );
        CreateMap<PricingItemViewModel, PricingItemDetailsDto>()
            .ForMember(
                dest => dest.DisplayName,
                opt => opt.ConvertUsing<LocalizationResolver, ILocalizableString>(src => src.DisplayName)
            );
        CreateMap<PricingItemViewModel, PricingItemDto>()
            .ForMember(
                dest => dest.DisplayName,
                opt => opt.ConvertUsing<LocalizationResolver, ILocalizableString>(src => src.DisplayName)
            );
        CreateMap<Price, PriceDto>();
        CreateMap<TrackAccountSubscription, TrackAccountSubscriptionDto>()
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );
        CreateMap<TrackAccountSubscription, TrackAccountSubscriptionDetailDto>()
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );

        CreateMap<Discount, DiscountDto>();
        CreateMap<DiscountCriteria, DiscountCriteriaDto>();

        CreateMap<PromoCode, PromoCodeDto>();
        CreateMap<Discount, SubscriptionDurationDiscountDto>()
            .ForMember(dest => dest.DurationInMonth,
                opt => opt.MapFrom(x => int.Parse(x.DiscountCriteriaList.First().SpecificationValue)));
        CreateMap<AddVehiclesRequest, AddVehiclesRequestDto>();

        CreateMap<Vehicle, VehicleDto>()
            .ForMember(dest => dest.ColorHex,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            );
        
        CreateMap<PrivacyPolicy, PrivacyPolicyDto>();

    }

    private static string ConvertColorToHexColor(Color color)
    {
        return $"0xFF{color.R:X2}{color.G:X2}{color.B:X2}";
    }

    private class AddressLocalizationResolver : IValueConverter<string, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public AddressLocalizationResolver(IStringLocalizerFactory stringLocalizerFactory) =>
            StringLocalizerFactory = stringLocalizerFactory;

        public string Convert(string sourceMember, ResolutionContext context) =>
            new LocalizableString(typeof(GeoNodeResource), sourceMember).Localize(StringLocalizerFactory);
    }

    private class GoTrackLocalizationResolver : IValueConverter<string, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public GoTrackLocalizationResolver(IStringLocalizerFactory stringLocalizerFactory)
        {
            StringLocalizerFactory = stringLocalizerFactory;
        }

        public string Convert(string sourceMember, ResolutionContext context) =>
            new LocalizableString(typeof(GoTrackResource), sourceMember).Localize(StringLocalizerFactory);
    }

    private class LocalizationResolver : IValueConverter<ILocalizableString, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public LocalizationResolver(IStringLocalizerFactory stringLocalizerFactory)
        {
            StringLocalizerFactory = stringLocalizerFactory;
        }

        public string Convert(ILocalizableString sourceMember, ResolutionContext context)
        {
            return sourceMember.Localize(StringLocalizerFactory);
        }
    }
}