using Fatora.Abstractions;
using Fatora.Abstractions.Exceptions;
using Fatora.Options;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Fatora;

public static class FatoraExtensions
{
#nullable disable
    public static IServiceCollection AddFatoraServices(this IServiceCollection services, Action<FatoraOptions> opt)
    {
        var option = new FatoraOptions();
        
        opt(option);
        
        if (option == null)
            throw new FatoraInvalidDataException("Fatora option cannot be null");

        if (string.IsNullOrWhiteSpace(option.Password))
            throw new FatoraInvalidDataException("Fatora password cannot be null or empty");

        if (string.IsNullOrWhiteSpace(option.UserName))
            throw new FatoraInvalidDataException("Fatora username cannot be null or empty");

        if (string.IsNullOrWhiteSpace(option.Url))
            throw new FatoraInvalidDataException("Fatora URL cannot be null or empty");

        if (string.IsNullOrWhiteSpace(option.TerminalId))
            throw new FatoraInvalidDataException("Fatora terminal id cannot be null or empty");

        services.Configure<FatoraOptions>(options =>
        {
            options.Password = option.Password;
            options.UserName = option.UserName;
            options.TerminalId = option.TerminalId;
            options.Url = option.Url;
        });

        services.AddTransient<IFatoraService, FatoraService>();

        services.AddLogging(b =>
        {
            b.SetMinimumLevel(option.MinLogLevel);
            b.AddConsole();
        });

        return services;
    }
}