using Microsoft.Extensions.Logging;

namespace Fatora.Options;

public class FatoraOptions
{
    public string Url { get; set; }
    public string UserName { get; set; }
    public string Password { get; set; }
    public string TerminalId { get; set; }
    public LogLevel MinLogLevel { get; set; }

    public FatoraOptions()
    {
        MinLogLevel = LogLevel.Debug;
    }

    public FatoraOptions(string url, string userName, string password, string terminalId,
        LogLevel minLogLevel = LogLevel.Debug)
    {
        Url = url;
        UserName = userName;
        Password = password;
        TerminalId = terminalId;
        MinLogLevel = minLogLevel;
    }
}