using GoTrack.Alerts.CheckOverSpeed.Models;
using Microsoft.EntityFrameworkCore;

namespace GoTrack.Alerts.CheckOverSpeed;

public class GoTrackAlertCheckOverSpeedContext : DbContext
{
    public GoTrackAlertCheckOverSpeedContext()
    {
    }

    public GoTrackAlertCheckOverSpeedContext(DbContextOptions<GoTrackAlertCheckOverSpeedContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AlertList> AlertLists { get; set; }
    public virtual DbSet<GroupedAlertList> GroupedAlertLists { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            optionsBuilder.UseMySql("name=DefaultConnection", ServerVersion.Parse("5.7.36-mysql"));
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasCharSet("utf8mb4")
            .UseCollation("utf8mb4_general_ci");

        modelBuilder.Entity<AlertList>(entity =>
        {
            entity.ToTable("alert_list");

            entity.Property(e => e.Id)
                .HasColumnName("id");

            entity.Property(e => e.AffectiveFrom)
                .HasColumnType("datetime")
                .HasColumnName("affective_from");

            entity.Property(e => e.Imei)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("imei");

            entity.Property(e => e.IsAlerted)
                .HasColumnType("int(11)")
                .HasColumnName("is_alerted");

            entity.Property(e => e.LastAlertedAt)
                .HasMaxLength(20)
                .HasColumnName("last_alerted_at");

            entity.Property(e => e.LastCheckedAt)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("last_checked_at");

            entity.Property(e => e.SpeedLimit)
                .HasColumnType("int(11)")
                .HasColumnName("speed_limit");
        });

        modelBuilder.Entity<GroupedAlertList>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("grouped_alert_list");

            entity.Property(e => e.Ids)
                .HasColumnType("text")
                .HasColumnName("ids");

            entity.Property(e => e.SpeedLimit)
                .HasColumnType("int(11)")
                .HasColumnName("speed_limit");
        });
    }
}
