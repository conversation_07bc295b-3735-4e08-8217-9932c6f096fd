using GoTrack.Alerts.BaseChecker.Models;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.Threading.Tasks;
using Warp10Abstraction.Models;
using Warp10Abstraction.WarpLibs;

namespace GoTrack.Alerts.CheckOverSpeed.Models;

public class GroupedAlertList : GroupedAlertListBase
{
    public int SpeedLimit { get; set; }

    public override Task<ViolationResult[]> CheckAlert(
        IWarpLib warpLib,
        string fromDate,
        string toDate,
        IConfiguration configuration)
    {
        return warpLib.CheckImeisOverSpeed(Alerts.Select(x => x.Imei).ToArray(), SpeedLimit, fromDate, toDate);
    }
}
