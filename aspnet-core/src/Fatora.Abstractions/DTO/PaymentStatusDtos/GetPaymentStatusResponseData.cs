using System.Text.Json.Serialization;
using Fatora.Abstractions.Enums;

namespace Fatora.Abstractions.DTO.PaymentStatusDtos;

public class GetPaymentStatusResponseData
{
    [JsonInclude] public PaymentStatus Status { get; private set; }
    [JsonInclude] public DateTime CreationTimeStamp { get; private set; }
    [JsonInclude] public string Rrn { get; private set; }
    [JsonInclude] public int Amount { get; private set; }
    [JsonInclude] public uint TerminalId { get; private set; }
    [JsonInclude] public string? Notes { get; private set; }

    public GetPaymentStatusResponseData(string status, string creationTimeStamp, string rrn, int amount,
        string terminalId, string? notes)
    {
        Status = GetPaymentStatus(status);
        CreationTimeStamp = DateTime.Parse(creationTimeStamp);
        Rrn = rrn;
        Amount = amount;
        TerminalId = uint.Parse(terminalId);
        Notes = notes;
    }

    private PaymentStatus GetPaymentStatus(string status)
    {
        switch (status)
        {
            case "P":
            case "p":
                return PaymentStatus.Pending;
            case "A":
            case "a":
                return PaymentStatus.Accept;
            case "F":
            case "f":
                return PaymentStatus.Failed;
            case "C":
            case "c":
                return PaymentStatus.Cancel;
            default:
                return PaymentStatus.Failed;
        }
    }
}