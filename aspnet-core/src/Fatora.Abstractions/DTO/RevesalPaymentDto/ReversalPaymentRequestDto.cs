using Fatora.Abstractions.Enums;

namespace Fatora.Abstractions.DTO.RevesalPaymentDto;

public class ReversalPaymentRequestDto
{
    public string Language { get; private set; }
    public string PaymentId { get; private set; }

    public ReversalPaymentRequestDto(FatoraLanguage lang, Guid payment_id)
    {
        Language = lang == FatoraLanguage.Arabic ? "ar" : "en";
        PaymentId = payment_id.ToString();
    }
}