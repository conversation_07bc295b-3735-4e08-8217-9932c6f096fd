using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public interface IBusinessAccountSubscriptionRequestAppService
{
    Task<PagedResultDto<BusinessAccountSubscriptionRequestDto>> GetListAsync(PagedResultRequestDto input);
    Task<BusinessAccountSubscriptionRequestDetailsDto> GetAsync(Guid id);
    Task StartProcessingAsync(Guid id,StartProcessingDto createDto);
    Task InstallDevicesAsync(Guid id, InstallDevicesDto createDto);
    Task FinishProcessingAsync(Guid id , FinishProcessingDto finishProcessingDto);
    Task RejectAsync(Guid id, RejectReqDto dto);
}