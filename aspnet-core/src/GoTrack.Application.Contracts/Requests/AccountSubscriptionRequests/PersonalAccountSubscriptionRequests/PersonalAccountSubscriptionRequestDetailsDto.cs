using System;
using System.Collections.Generic;
using GoTrack.Addresses;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

public class PersonalAccountSubscriptionRequestDetailsDto
{
    public Guid OwnerId { get; set; }
    public string OwnerFirstName { get; set; } = string.Empty;
    public string OwnerLastName { get; set; } = string.Empty;
    public string OwnerEmail { get; set; } = string.Empty;
    public string OwnerPhoneNumber { get; set; } = string.Empty;
    public RequestStatus Status { get; set; }
    public TrackerInstallationLocation TrackerInstallationLocation { get; set; }
    public List<SubscriptionVehicleInfoDto> TrackVehicles { get; set; } = [];
    public AccountSubscriptionRequestStage Stage { get; set; }
    public string SubscriptionPlanKey { get;  set; }
    public string SubscriptionPlanLocalizedName { get; set; } = string.Empty;
    public int UserCount { get; set; }
    public string? PaymentUrl { get; set; }
    public AddressDto? Address { get; set; }
}