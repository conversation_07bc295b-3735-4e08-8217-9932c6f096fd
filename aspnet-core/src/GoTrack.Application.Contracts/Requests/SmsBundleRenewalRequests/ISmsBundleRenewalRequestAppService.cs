using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Requests.SmsBundleRenewalRequests.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Requests.SmsBundleRenewalRequests;

public interface ISmsBundleRenewalRequestAppService : IApplicationService
{
    Task<SmsBundleRenewalRequestDetailsDto> GetAsync(Guid id);
    Task<PagedResultDto<SmsBundleRenewalRequestDto>> GetListAsync(PagedResultRequestDto input);
}