using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Requests;

public interface IRequestAppService : IApplicationService
{
    Task<PagedResultDto<RequestDto>> GetAsync(RequestTypePagedResultRequestDto requestDto);
    
    Task<PagedResultDto<RequestNoteDto>> GetNoteAsync(Guid id , PagedResultRequestDto requestDto);
    Task PayWithCashAsync(Guid id, CashPaymentDto input);
}