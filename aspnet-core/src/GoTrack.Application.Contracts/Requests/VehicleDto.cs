using GoTrack.Vehicles.LicensePlates;
using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Requests;

public class VehicleDto : AuditedEntityDto<Guid>
{
    public string ColorHex { get; set; } = string.Empty;
    public string LicensePlateSerial { get; set; } = string.Empty;
    public VehicleLicensePlateSubClass LicensePlateSubClass { get; set; }
    public double ConsumptionRate { get; set; }
}