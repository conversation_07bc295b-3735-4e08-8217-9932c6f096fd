using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Localization;

namespace GoTrack.Payments.PricingItems.DTOs;

public class PricingItemDetailsDto 
{
    public string Key { get; set; }
    public string DisplayName { get; set; }
    public PricingType PricingType { get; set; }
    public decimal CurrentPrice { get; set; }
    public DateTime LastPriceChangeDate { get; set; }
    public List<PriceDto> LastPriceChanges { get; set; } = [];
}