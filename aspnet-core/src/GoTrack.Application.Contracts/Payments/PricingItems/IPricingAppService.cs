using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Payments.PricingItems.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Payments.PricingItems;

public interface IPricingAppService : IApplicationService
{
    Task<PricingItemDetailsDto> GetByKeyAsync(string key);
    Task<PagedResultDto<PricingItemDto>> GetListAsync(PagedResultRequestDto input);
    Task<PagedResultDto<PricingItemDto>> GetUnsetListAsync(PagedResultRequestDto input);
    Task SetAsync(SetPricingItemDto input);
    Task SetListAsync(SetPricingItemListDto input);
}