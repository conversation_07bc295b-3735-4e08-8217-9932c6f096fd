using GoTrack.Payments.Discounts.DTOs;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Payments.Discounts;

public interface ISubscriptionDurationDiscountAppService : IApplicationService
{
    Task CreateAsync(CreateSubscriptionDurationDiscountDto input);
    Task DeleteAsync(Guid id);
    Task<PagedResultDto<SubscriptionDurationDiscountDto>> GetListAsync(PagedResultRequestDto input);
}
