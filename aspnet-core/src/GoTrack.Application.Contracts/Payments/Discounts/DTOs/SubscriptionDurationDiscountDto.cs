using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace GoTrack.Payments.Discounts.DTOs;

public class SubscriptionDurationDiscountDto : FullAuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public TargetType TargetType { get; set; }
    public decimal Value { get; set; }
    public bool IsPercentage { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int DurationInMonth { get; private set; }
    public List<DiscountCriteriaDto> DiscountCriteriaDtos { get; set; } = new();
}