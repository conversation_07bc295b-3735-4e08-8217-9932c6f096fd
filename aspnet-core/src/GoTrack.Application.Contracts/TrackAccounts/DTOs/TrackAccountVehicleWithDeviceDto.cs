using System;
using GoTrack.Vehicles.LicensePlates;

namespace GoTrack.TrackAccounts.DTOs;

public class TrackAccountVehicleWithDeviceDto
{
    public Guid Id { get; set; }
    public string LicensePlateSerial { get; set; } = string.Empty;
    public VehicleLicensePlateSubClass LicensePlateSubClass { get; set; } 
    public DateTime CreationTime { get; set; }
    public string? DeviceImei { get; set; }
    public DateTime? DeviceLinkedAt { get; set; }
}
