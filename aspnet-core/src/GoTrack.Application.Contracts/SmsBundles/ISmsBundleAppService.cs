using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.SmsBundles.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.SmsBundles;

public interface ISmsBundleAppService : IApplicationService
{
    Task<SmsBundleDto> CreateAsync(SmsBundleCreateDto input);
    Task<SmsBundleDto> GetAsync(Guid id);
    Task<PagedResultDto<SmsBundleDto>> GetListAsync(PagedResultRequestDto input);
    Task<SmsBundleDto> UpdateAsync(Guid id, SmsBundleCreateDto input);
    Task DeleteAsync(Guid id);
}