using GoTrack.Alerts.CheckJobTime.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;

namespace GoTrack.Alerts.CheckJobTime;

public class GoTrackAlertCheckJobTimeContext : DbContext
{
    public GoTrackAlertCheckJobTimeContext()
    {
    }

    public GoTrackAlertCheckJobTimeContext(DbContextOptions<GoTrackAlertCheckJobTimeContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AlertList> AlertLists { get; set; }
    public virtual DbSet<GroupedAlertList> GroupedAlertLists { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            optionsBuilder.UseMySql("name=DefaultConnection", ServerVersion.Parse("5.7.36-mysql"));
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasCharSet("utf8mb4")
            .UseCollation("utf8mb4_general_ci");

        modelBuilder.HasCharSet("utf8mb4")
            .UseCollation("utf8mb4_general_ci");

        modelBuilder.Entity<AlertList>(entity =>
        {
            entity.ToTable("alert_list");

            entity.Property(e => e.Id)
                .HasColumnName("id");

            entity.Property(e => e.AffectiveFrom)
                .HasColumnType("datetime")
                .HasColumnName("affective_from");

            entity.Property(e => e.Imei)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("imei");

            entity.Property(e => e.IsAlerted)
                .HasColumnType("int(11)")
                .HasColumnName("is_alerted");

            entity.Property(e => e.LastAlertedAt)
                .HasMaxLength(20)
                .HasColumnName("last_alerted_at");

            entity.Property(e => e.LastCheckedAt)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("last_checked_at");

            entity.Property(e => e.Name)
                .IsRequired()
                .HasColumnName("name");

            entity.Property(e => e.StartTime)
                .HasColumnType("time")
                .HasColumnName("start_time");

            entity.Property(e => e.EndTime)
                .HasColumnType("time")
                .HasColumnName("end_time");

            entity.Property(e => e.DaysOfWeek)
                .HasConversion(
                    v => string.Join(',', v.Select(s => s.ToString())),

                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(s => (DayOfWeek)Enum.Parse(typeof(DayOfWeek), s))
                      .ToList()
                )
                .HasColumnType("varchar(255)")
                .HasColumnName("days_of_week");
        });

        modelBuilder.Entity<GroupedAlertList>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("grouped_alert_list");

            entity.Property(e => e.Ids)
                .HasColumnType("text")
                .HasColumnName("ids");

            entity.Property(e => e.StartTime)
                .HasColumnType("time")
                .HasColumnName("start_time");

            entity.Property(e => e.EndTime)
                .HasColumnType("time")
                .HasColumnName("end_time");

            entity.Property(e => e.DaysOfWeek)
                .HasConversion(
                    v => string.Join(',', v.Select(s => s.ToString())),

                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(s => (DayOfWeek)Enum.Parse(typeof(DayOfWeek), s))
                      .ToList()
                )
                .HasColumnType("varchar(255)")
                .HasColumnName("days_of_week");
        });
    }
}
