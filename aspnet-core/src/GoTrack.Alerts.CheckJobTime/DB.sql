
SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

CREATE DATABASE `GoTrack_Alerts_CheckJobTime_JT_01` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci */;
USE `GoTrack_Alerts_CheckJobTime_JT_01`;

CREATE TABLE `alert_list` (
  `Id` CHAR(36) CHARACTER SET ascii COLLATE ascii_general_ci NOT NULL,
  `affective_from` DATETIME NOT NULL,
  `last_checked_at` VARCHAR(20) NOT NULL,
  `imei` VARCHAR(20) NOT NULL,

  `name` LONGTEXT NOT NULL,
  `start_time` TIME(6) NOT NULL,
  `end_time` TIME(6) NOT NULL,
  `days_of_week` LONGTEXT NOT NULL,

  `is_alerted` INT(11) NOT NULL,
  `sat_from` INT(11) NOT NULL DEFAULT 0,
  `sat_to` INT(11) NOT NULL DEFAULT 0,
  `sun_from` INT(11) NOT NULL DEFAULT 0,
  `sun_to` INT(11) NOT NULL DEFAULT 0,
  `mon_from` INT(11) NOT NULL DEFAULT 0,
  `mon_to` INT(11) NOT NULL DEFAULT 0,
  `tue_from` INT(11) NOT NULL DEFAULT 0,
  `tue_to` INT(11) NOT NULL DEFAULT 0,
  `wed_from` INT(11) NOT NULL DEFAULT 0,
  `wed_to` INT(11) NOT NULL DEFAULT 0,
  `thu_from` INT(11) NOT NULL DEFAULT 0,
  `thu_to` INT(11) NOT NULL DEFAULT 0,
  `fri_from` INT(11) NOT NULL DEFAULT 0,
  `fri_to` INT(11) NOT NULL DEFAULT 0,
  `last_alerted_at` VARCHAR(20) DEFAULT NULL,
  PRIMARY KEY (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



-- Create view grouping by Name, StartTime, EndTime, DaysOfWeek
CREATE VIEW `grouped_alert_list` AS
SELECT
  `alert_list`.`start_time`  AS `start_time`,
  `alert_list`.`end_time`    AS `end_time`,
  `alert_list`.`days_of_week` AS `days_of_week`,
  GROUP_CONCAT(`alert_list`.`Id` SEPARATOR ',') AS `ids`
FROM
  `alert_list`
GROUP BY
  `alert_list`.`start_time`,
  `alert_list`.`end_time`,
  `alert_list`.`days_of_week`,
  substr(`alert_list`.`last_checked_at`,1,7)
ORDER BY
  `alert_list`.`start_time`,
  `alert_list`.`end_time`,
  `alert_list`.`days_of_week`,
  substr(`alert_list`.`last_checked_at`,1,7);
