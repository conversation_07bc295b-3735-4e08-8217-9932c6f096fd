using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.FCMDevices.EntityConfiguration;

public class FcmDeviceEntityConfiguration : IEntityTypeConfiguration<FcmDevice>
{
    public void Configure(EntityTypeBuilder<FcmDevice> builder)
    {
        builder.ConfigureByConvention();
        builder.HasKey(d => d.Id);
        builder.HasIndex(d => d.FcmToken).IsUnique();
        builder.Property(d => d.FcmToken).IsRequired().HasMaxLength(500);
        builder.Property(d => d.DeviceId).HasMaxLength(500);
        builder.Property(d => d.DeviceType).IsRequired();
        builder.Property(d => d.Name).HasMaxLength(100);
        builder.ToGoTrackTable();
    }
}