using GoTrack.AlertDefinitions.DisassembleTrackingDevices;
using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration.DisassembleTrackingDevices;

public class DisassembleTrackingDeviceAlertDefinitionEntityConfiguration :  IEntityTypeConfiguration<DisassembleTrackingDeviceAlertDefinition>
{
    public void Configure(EntityTypeBuilder<DisassembleTrackingDeviceAlertDefinition> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}