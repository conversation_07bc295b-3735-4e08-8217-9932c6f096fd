using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.EntityFrameworkCore;
using GoTrack.TrackableEntities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration.AlertDefinitionAssociations;

public class AlertDefinitionAssociationEntityConfiguration : IEntityTypeConfiguration<AlertDefinitionAssociation>
{
    public void Configure(EntityTypeBuilder<AlertDefinitionAssociation> builder)
    {
        builder.ConfigureByConvention();

        builder.HasOne(x => x.TrackableEntityAssociation);

        builder.ToGoTrackTable();
    }
}
