using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.PermissionManagement;

namespace GoTrack.PermissionGrants.EntityConfigurations;

public class PermissionGrantEntityConfiguration : IEntityTypeConfiguration<PermissionGrant>
{
    public void Configure(EntityTypeBuilder<PermissionGrant> builder)
    {
        builder.Property(permissionGrant => permissionGrant.ProviderKey).HasMaxLength(72);
    }
}