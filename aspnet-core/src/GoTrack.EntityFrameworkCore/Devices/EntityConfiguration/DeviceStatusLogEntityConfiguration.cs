using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Devices.EntityConfiguration;

public class DeviceStatusLogEntityConfiguration : IEntityTypeConfiguration<DeviceStatusLog>
{
    public void Configure(EntityTypeBuilder<DeviceStatusLog> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
