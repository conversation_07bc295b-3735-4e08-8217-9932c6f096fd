using GoTrack.Notifications;
using Microsoft.Extensions.Logging;
using Notify.Provider.FCM;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace GoTrack.FCM;

public class FCMSender : IFCMSender, ITransientDependency
{
    private readonly IFirebaseNotificationSender _firebaseNotificationSender;
    private readonly ILogger<FCMSender> _logger;

    public FCMSender(IFirebaseNotificationSender firebaseNotificationSender, ILogger<FCMSender> logger)
    {
        _firebaseNotificationSender = firebaseNotificationSender;
        _logger = logger;
    }

    public Task<IEnumerable<string>> SendAsync(FCMMessage fCMMessage)
    {
        foreach (var token in fCMMessage.UserDeviceTokens) 
        {
            _logger.LogInformation("Sending FCM notification with " +
                "body: {Body} " +
                "to device token: {token}", 
                fCMMessage.Body,
                token
            );
        }

        var result = _firebaseNotificationSender.SendPushNotificationAsync(
            fCMMessage.Title,
            fCMMessage.Body,
            fCMMessage.UserDeviceTokens
        );

        foreach (var token in fCMMessage.UserDeviceTokens)
        {
            _logger.LogInformation("FCM notification with " +
                "body: {Body} " +
                "to device token: {token} " +
                "has been sent successfully",
                fCMMessage.Body,
                token
            );
        }

        return result;
    }
}
