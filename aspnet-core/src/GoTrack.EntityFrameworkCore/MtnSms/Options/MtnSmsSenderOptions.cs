using GoTrack.Localization;
using Volo.Abp.Localization;
using Volo.Abp.Settings;

namespace GoTrack.MtnSms.Options;

public class MtnSmsSenderSettingProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        context.Add(
            new SettingDefinition(MtnSmsSenderSettingKeys.SenderId, null, L(MtnSmsSenderSettingKeys.SenderId), null,
                false, true, false),
            new SettingDefinition(MtnSmsSenderSettingKeys.Username, null, L(MtnSmsSenderSettingKeys.Username), null,
                false, true, false),
            new SettingDefinition(MtnSmsSenderSettingKeys.Password, null, L(MtnSmsSenderSettingKeys.Password), null,
                false, true, true)
        );
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<GoTrackResource>(name);
    }
}