using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class PersonalAccountSubscriptionRequest_Address_Optional : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Address_Street",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Governorate",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Country",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Address_City",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Area",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "longtext")
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "GoTrackPersonalAccountSubscriptionRequests",
                keyColumn: "Address_Street",
                keyValue: null,
                column: "Address_Street",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Street",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "GoTrackPersonalAccountSubscriptionRequests",
                keyColumn: "Address_Governorate",
                keyValue: null,
                column: "Address_Governorate",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Governorate",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "GoTrackPersonalAccountSubscriptionRequests",
                keyColumn: "Address_Country",
                keyValue: null,
                column: "Address_Country",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Country",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "GoTrackPersonalAccountSubscriptionRequests",
                keyColumn: "Address_City",
                keyValue: null,
                column: "Address_City",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Address_City",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.UpdateData(
                table: "GoTrackPersonalAccountSubscriptionRequests",
                keyColumn: "Address_Area",
                keyValue: null,
                column: "Address_Area",
                value: "");

            migrationBuilder.AlterColumn<string>(
                name: "Address_Area",
                table: "GoTrackPersonalAccountSubscriptionRequests",
                type: "longtext",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "longtext",
                oldNullable: true)
                .Annotation("MySql:CharSet", "utf8mb4")
                .OldAnnotation("MySql:CharSet", "utf8mb4");
        }
    }
}
