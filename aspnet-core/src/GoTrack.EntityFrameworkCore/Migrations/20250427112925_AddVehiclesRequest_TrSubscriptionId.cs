using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class AddVehiclesRequest_TrSubscriptionId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "TrackAccountSubscriptionId",
                table: "GoTrackAddVehiclesRequests",
                type: "char(36)",
                nullable: false,
                defaultValue: new Guid("********-0000-0000-0000-********0000"),
                collation: "ascii_general_ci");

            migrationBuilder.CreateIndex(
                name: "IX_GoTrackAlertTriggers_VehicleId",
                table: "GoTrackAlertTriggers",
                column: "VehicleId");

            migrationBuilder.AddForeignKey(
                name: "FK_GoTrackAlertTriggers_GoTrackVehicles_VehicleId",
                table: "GoTrackAlertTriggers",
                column: "VehicleId",
                principalTable: "GoTrackVehicles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_GoTrackAlertTriggers_GoTrackVehicles_VehicleId",
                table: "GoTrackAlertTriggers");

            migrationBuilder.DropIndex(
                name: "IX_GoTrackAlertTriggers_VehicleId",
                table: "GoTrackAlertTriggers");

            migrationBuilder.DropColumn(
                name: "TrackAccountSubscriptionId",
                table: "GoTrackAddVehiclesRequests");
        }
    }
}
