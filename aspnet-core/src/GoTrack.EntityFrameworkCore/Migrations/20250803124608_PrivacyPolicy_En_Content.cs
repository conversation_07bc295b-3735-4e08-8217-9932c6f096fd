using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class PrivacyPolicy_En_Content : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ContentEn",
                table: "GoTrackPrivacyPolicys",
                type: "varchar(8000)",
                maxLength: 8000,
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContentEn",
                table: "GoTrackPrivacyPolicys");
        }
    }
}
