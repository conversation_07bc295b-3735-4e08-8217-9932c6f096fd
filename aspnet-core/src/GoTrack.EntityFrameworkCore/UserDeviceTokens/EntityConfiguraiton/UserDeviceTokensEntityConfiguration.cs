using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.UserDeviceTokens.EntityConfiguraiton;

public class UserDeviceTokensEntityConfiguration : IEntityTypeConfiguration<UserDeviceToken>
{
    public void Configure(EntityTypeBuilder<UserDeviceToken> builder)
    {
        builder.ToTable("UserDeviceToken");

        builder.ConfigureByConvention();

        builder.ApplyObjectExtensionMappings();
    }
}
