using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace GoTrack.UserDeviceTokens;

public class UserDeviceTokenRepository : EfCoreRepository<GoTrackDbContext, UserDeviceToken, Guid>,
    IUserDeviceTokenRepository
{
    public UserDeviceTokenRepository(IDbContextProvider<GoTrackDbContext> dbContextProvider) : base(dbContextProvider)
    {
    }

    public async Task<List<UserDeviceToken>> GetListAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await (await GetDbSetAsync())
            .Where(a => a.UserId == userId && !a.Is<PERSON>eleted)
            .ToListAsync(GetCancellationToken(cancellationToken));
    }
}