using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers;

public class ExitingZoneAlertTriggerEntityConfiguration : IEntityTypeConfiguration<ExitingZoneAlertTrigger>
{
    public void Configure(EntityTypeBuilder<ExitingZoneAlertTrigger> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
