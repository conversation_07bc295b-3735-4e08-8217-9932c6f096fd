using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers;

public class ExitingRouteAlertTriggerEntityConfiguration : IEntityTypeConfiguration<ExitingRouteAlertTrigger>
{
    public void Configure(EntityTypeBuilder<ExitingRouteAlertTrigger> builder)
    {
        builder.ConfigureByConvention();

        builder.ToGoTrackTable();
    }
}
