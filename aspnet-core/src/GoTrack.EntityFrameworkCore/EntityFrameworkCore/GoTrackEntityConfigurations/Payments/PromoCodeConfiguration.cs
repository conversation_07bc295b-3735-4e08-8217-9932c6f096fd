using GoTrack.Payments.PromoCodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Payments;

public class PromoCodeConfiguration : IEntityTypeConfiguration<PromoCode>
{
    public void Configure(EntityTypeBuilder<PromoCode> builder)
    {
        builder.ConfigureByConvention();
        builder.ToGoTrackTable();
        builder.Property(x => x.Code)
            .IsRequired()
            .HasMaxLength(8);
        builder.HasIndex(x => x.Code);
    }
}