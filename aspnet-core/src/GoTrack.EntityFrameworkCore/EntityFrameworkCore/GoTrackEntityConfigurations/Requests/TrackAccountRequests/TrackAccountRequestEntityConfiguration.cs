using GoTrack.Requests;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Requests.TrackAccountRequests;

public class TrackAccountRequestEntityConfiguration : IEntityTypeConfiguration<TrackAccountRequest>
{
    public void Configure(EntityTypeBuilder<TrackAccountRequest> builder)
    {
        builder.ToGoTrackTable();
        builder.ConfigureByConvention();
    }
}
