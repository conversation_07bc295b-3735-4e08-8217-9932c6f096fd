using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using GoTrack.Data;
using Volo.Abp.DependencyInjection;

namespace GoTrack.EntityFrameworkCore;

public class EntityFrameworkCoreGoTrackDbSchemaMigrator
    : IGoTrackDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreGoTrackDbSchemaMigrator(
        IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolve the GoTrackDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<GoTrackDbContext>()
            .Database
            .MigrateAsync();
    }
}
