using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.TripTemplates.EntityConfiguraiton;

public class TripTemplateEntityConfiguration : IEntityTypeConfiguration<TripTemplate>
{
    public void Configure(EntityTypeBuilder<TripTemplate> builder)
    {
        builder.ConfigureByConvention();

        builder.OwnsMany(x => x.TripTemplateRoutes);

        builder.ToGoTrackTable();
    }
}
