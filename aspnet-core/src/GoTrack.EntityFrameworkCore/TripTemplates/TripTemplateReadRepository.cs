using GoTrack.EntityFrameworkCore;
using GoTrack.Routes;
using GoTrack.Routes.RouteViewModels;
using GoTrack.StopPoints;
using GoTrack.Trips;
using GoTrack.TripTemplates.TripTemplateViewModels;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities;

namespace GoTrack.TripTemplates;

public class TripTemplateReadRepository : ITripTemplateReadRepository, IScopedDependency
{
    private readonly GoTrackDbContext _context;
    private DbSet<TripTemplate> _dbSet => _context.Set<TripTemplate>();

    public TripTemplateReadRepository(GoTrackDbContext context)
    {
        _context = context;
    }

    public async Task<TripTemplateViewModel> GetTripTemplateViewModelAsync(Guid tripTemplateId)
    {
        var tripTemplate = await _dbSet.Include(x => x.TripTemplateRoutes).FirstOrDefaultAsync(x => x.Id == tripTemplateId)
            ?? throw new EntityNotFoundException(typeof(TripTemplate));

        var routeIdsInTripTemplate = tripTemplate.TripTemplateRoutes.Select(x => x.RouteId).ToList();

        var routes = await _context.Set<Route>().Include(x => x.StopPoints)
            .Where(x => routeIdsInTripTemplate.Contains(x.Id))
            .ToListAsync();

        var routeViewModels = new List<RouteViewModel>();
        foreach (var route in routes)
        {
            var stopPointIdsInRoute = route.StopPoints.Select(x => x.StopPointId).ToList();

            var routeStopPoints = await _context.Set<StopPoint>()
                .Where(x => stopPointIdsInRoute.Contains(x.Id))
                .ToListAsync();

            routeViewModels.Add(RouteViewModel.GetRouteViewModelFromRoute(route, routeStopPoints));
        }

        var tripQuery = _context.Set<Trip>().Where(x => x.TripTemplateId == tripTemplateId);

        await tripQuery.OfType<TripVehicle>().Include(x => x.Vehicle).LoadAsync();
        await tripQuery.OfType<TripVehicleGroup>().Include(x => x.VehicleGroup).LoadAsync();

        var trips = await tripQuery.ToListAsync();

        var vehicles = new List<Vehicle>();
        var vehicleGroups = new List<VehicleGroup>();
        foreach (var trip in trips)
        {
            switch (trip)
            {
                case TripVehicle vt:
                    vehicles.Add(vt.Vehicle);
                    break;

                case TripVehicleGroup vgt:
                    vehicleGroups.Add(vgt.VehicleGroup);
                    break;
            };
        }

        return TripTemplateViewModel.GetTripTemplateViewModelFromTripTemplate(
            tripTemplate,
            routeViewModels,
            vehicles,
            vehicleGroups
        );
    }
}
