using GoTrack.Alerts.CheckZoneInOut.Models;
using Microsoft.EntityFrameworkCore;

namespace GoTrack.Alerts.CheckZoneInOut;

public class GoTrackAlertCheckZoneInOutContext : DbContext
{
    public GoTrackAlertCheckZoneInOutContext()
    {
    }

    public GoTrackAlertCheckZoneInOutContext(DbContextOptions<GoTrackAlertCheckZoneInOutContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AlertList> AlertLists { get; set; }
    public virtual DbSet<GroupedAlertList> GroupedAlertLists { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see http://go.microsoft.com/fwlink/?LinkId=723263.
            optionsBuilder.UseMySql("server=localhost;database=gps21_c;user=root;treattinyasboolean=true;connection timeout=300;default command timeout=300", ServerVersion.Parse("5.7.36-mysql"));
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasCharSet("utf8mb4")
            .UseCollation("utf8mb4_general_ci");

        modelBuilder.Entity<AlertList>(entity =>
        {
            entity.ToTable("alert_list");

            entity.Property(e => e.Id)
                .HasColumnName("id");

            entity.Property(e => e.AffectiveFrom)
                .HasColumnType("datetime")
                .HasColumnName("affective_from");

            entity.Property(e => e.Imei)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("imei");

            entity.Property(e => e.IsAlerted)
                .HasColumnType("int(11)")
                .HasColumnName("is_alerted");

            entity.Property(e => e.LastAlertedAt)
                .HasMaxLength(20)
                .HasColumnName("last_alerted_at");

            entity.Property(e => e.LastCheckedAt)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("last_checked_at");

            entity.Property(e => e.Polygon)
                .IsRequired()
                .HasMaxLength(4000)
                .HasColumnName("polygon");
        });

        modelBuilder.Entity<GroupedAlertList>(entity =>
        {
            entity.HasNoKey();

            entity.ToView("grouped_alert_list");

            entity.Property(e => e.Ids)
                .HasColumnType("text")
                .HasColumnName("ids");

            entity.Property(e => e.Polygon)
                .IsRequired()
                .HasMaxLength(4000)
                .HasColumnName("polygon");
        });
    }
}
