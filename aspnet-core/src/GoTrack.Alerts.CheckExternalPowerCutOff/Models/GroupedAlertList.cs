using GoTrack.Alerts.BaseChecker.Models;
using Microsoft.Extensions.Configuration;
using System;
using System.Linq;
using System.Threading.Tasks;
using Warp10Abstraction.Models;
using Warp10Abstraction.WarpLibs;

namespace GoTrack.Alerts.CheckExternalPowerCutOff.Models;

public class GroupedAlertList : GroupedAlertListBase
{
    public override Task<ViolationResult[]> CheckAlert(
        IWarpLib warpLib,
        string fromDate,
        string toDate,
        IConfiguration configuration)
    {
        return warpLib.CheckImeisExternalPowerCutOffAsync(Alerts.Select(x => x.Imei).ToArray(), fromDate, toDate);
    }
}
