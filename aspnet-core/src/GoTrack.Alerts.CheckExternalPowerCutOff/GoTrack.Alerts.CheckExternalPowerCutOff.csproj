<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <UserSecretsId>dotnet-GoTrack.Alerts.CheckExternalPowerCutOff-3A2B4C5D-6E7F-8G9H-1I2J-3K4L5M6N7O8P</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoTrack.Alerts.BaseChecker\GoTrack.Alerts.BaseChecker.csproj" />
  </ItemGroup>

</Project>
