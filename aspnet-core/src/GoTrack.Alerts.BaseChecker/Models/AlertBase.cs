using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using GoTrack.Alerts.Contracts.Exchanges.RaisedAlert;
using MassTransit;
using Warp10Abstraction.Models;

namespace GoTrack.Alerts.BaseChecker.Models;

public abstract class AlertBase
{
    public Guid Id { get; set; }
    public string Imei { get; set; }
    public DateTime AffectiveFrom { get; set; }
    public string LastCheckedAt { get; set; }
    public int IsAlerted { get; set; }
    public string LastAlertedAt { get; set; }

    public void UpdateLastAlertAt(ViolationResult violationResult)
    {
        if (violationResult.ViolatedIntervals.Length > 0)
        {
            if (violationResult.LastTimestamp == violationResult.ViolatedIntervals[^1].GetLastTimestamp())
                LastAlertedAt = violationResult.LastTimestamp;
            else
                LastAlertedAt = null;
        }

        else if (violationResult.LastTimestamp != "" && violationResult.LastTimestamp != "0")
            LastAlertedAt = null;
    }

    public async Task<int> ReportNewAlert(
        IPublishEndpoint publishEndpoint,
        CancellationToken cancellationToken,
        ViolationResult violationResult,
        ActivitySource activitySource)
    {
        bool firstIter = true;
        var filteredIntervals = violationResult.MergedViolatedIntervals
            .Where(x => long.Parse(x.Value) > long.Parse(LastCheckedAt))
            .ToArray();

        foreach (KeyValuePair<string, string> mergedResult in filteredIntervals)
        {
            using var activity = activitySource.StartActivity();

            using var source = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            var mergedTokens = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, source.Token);

            string startTimestamp = mergedResult.Key;
            if (firstIter)
            {
                if (startTimestamp == violationResult.FirstTimestamp && LastAlertedAt is not null)
                    startTimestamp = LastAlertedAt;

                firstIter = false;
            }

            await publishEndpoint.Publish(new RaisedAlert()
            {
                AlertTriggerId = Id,
                StartTimestamp = startTimestamp,
                EndTimestamp = mergedResult.Value,
            },
                mergedTokens.Token
            );

            activity?.SetTag("message.alert_trigger_id", Id);
            activity?.SetTag("message.start_timestamp", startTimestamp);
            activity?.SetTag("message.end_timestamp", mergedResult.Value);
            activity?.SetTag("message.start_datetime", FromTimeStampToDateTime(startTimestamp));
            activity?.SetTag("message.end_datetime", FromTimeStampToDateTime(mergedResult.Value));
            activity?.SetStatus(ActivityStatusCode.Ok);
            
            source.Dispose();
            mergedTokens.Dispose();
        }
        
        return filteredIntervals.Length;
    }
    
    private DateTime FromTimeStampToDateTime(string timeStamp)
    {
        return DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(timeStamp))
            .ToLocalTime()
            .DateTime;
    }
}