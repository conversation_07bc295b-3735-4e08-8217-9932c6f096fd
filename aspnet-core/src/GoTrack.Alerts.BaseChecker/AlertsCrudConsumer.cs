using System.Threading.Tasks;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace GoTrack.Alerts.BaseChecker;

public class AlertsCrudConsumer : IConsumer<AlertCrudToService>
{
    private readonly IRepoService _repoService;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly ILogger<AlertsCrudConsumer> _logger;

    public AlertsCrudConsumer(
        IRepoService repoService,
        IPublishEndpoint publishEndpoint,
        ILogger<AlertsCrudConsumer> logger)
    {
        _repoService = repoService;
        _publishEndpoint = publishEndpoint;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<AlertCrudToService> context)
    {
        AlertCrudToService alert = context.Message;

        _logger.LogDebug("Received alert, AlertId: {}, Type: {}", alert.AlertCrud.Id, alert.AlertCrud.CrudType);

        if (alert.AlertCrud.CrudType == CrudType.Delete)
        {
            var alertBase = _repoService.GetAlert(alert.AlertCrud.Id);

            if (alertBase is not null)
                await _repoService.DeleteAlert(alertBase);

            _logger.LogDebug("Alert deleted, AlertId: {}", alert.AlertCrud.Id);

            return;
        }

        if (alert.AlertCrud.CrudType is CrudType.Update or CrudType.Add)
        {
            await _repoService.ParseAndSaveAlert(alert);

            _logger.LogDebug("Alert added/modified, AlertId: {}", alert.AlertCrud.Id);
        }
    }
}