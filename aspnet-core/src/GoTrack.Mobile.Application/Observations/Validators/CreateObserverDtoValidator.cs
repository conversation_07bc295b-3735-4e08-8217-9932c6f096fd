using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.Observations.DTOs;
using GoTrack.Msisdns;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;

namespace GoTrack.Mobile.Observations.Validators;

public class CreateObserverDtoValidator : AbstractValidator<CreateObserverDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;
    private readonly IMsisdnManager _manager;

    public CreateObserverDtoValidator(IStringLocalizer<GoTrackResource> localizer, IMsisdnManager manager)
    {
        _localizer = localizer;
        _manager = manager;

        RuleFor(x => x.Name.Trim())
            .NotEmpty()
            .Must(x => x.Length >= 2)
            .WithName(_localizer["GoTrack:Name"]);

        RuleFor(x => x.PhoneNumber)
            .NotEmpty()
            .Must(x => _manager.Create(x) is Msisdn)
            .WithName(_localizer["GoTrack:PhoneNumber"]);

        RuleFor(x => x.VehicleIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleGroupIds.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:VehicleIds"]);

        RuleFor(x => x.VehicleGroupIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleIds.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:VehicleGroupIds"]);
    }

}
