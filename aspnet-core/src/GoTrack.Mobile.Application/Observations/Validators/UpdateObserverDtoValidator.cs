using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.Observations.DTOs;
using GoTrack.Msisdns;
using Microsoft.Extensions.Localization;

namespace GoTrack.Mobile.Observations.Validators;

public class UpdateObserverDtoValidator : AbstractValidator<UpdateObserverDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;
    private readonly IMsisdnManager _manager;

    public UpdateObserverDtoValidator(IStringLocalizer<GoTrackResource> localizer, IMsisdnManager manager)
    {
        _localizer = localizer;
        _manager = manager;

        RuleFor(x => x.UserTrackAccountAssociationId)
            .NotEmpty()
            .WithName(_localizer["GoTrack:UserTrackAccountAssociationId"]);

        RuleFor(x => x.Name.Trim())
            .NotEmpty()
            .Must(x => x.Length >= 2)
            .WithName(_localizer["GoTrack:Name"]);

        RuleFor(x => x.PhoneNumber)
            .NotEmpty()
            .Must(x => _manager.Create(x) is Msisdn)
            .WithName(_localizer["GoTrack:PhoneNumber"]);
    }
}
