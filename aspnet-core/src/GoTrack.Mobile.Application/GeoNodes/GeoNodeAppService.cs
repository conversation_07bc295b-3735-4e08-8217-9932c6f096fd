using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.GeoNodes;
using GoTrack.Localization;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;

namespace GoTrack.Mobile.GeoNodes;


public class GeoNodeAppService : GoTrackMobileAppService, IGeoNodeAppService
{
    private readonly IRepository<GeoNode, Guid> _geoNodeRepository;

    public GeoNodeAppService(IRepository<GeoNode, Guid> geoNodeRepository)
    {
        _geoNodeRepository = geoNodeRepository;
    }
    
    public async Task<List<GeoNodeTreeDto>> Get()
    {
        var geoNodes = await _geoNodeRepository.GetListAsync();

        var result = new List<GeoNodeTreeDto>();
        
        if (geoNodes.IsNullOrEmpty())
        {
            return result;
        }
        
            
        foreach (var tree in geoNodes.GroupBy(i => i.TreeId))
        {
            var nodeListDto = new List<GeoNodeDto>();

            foreach (var node in tree.ToList())
            {
                nodeListDto.Add(new GeoNodeDto
                {
                    NodeId = node.Id,
                    Name = node.Name,
                    Type = node.NodeType.ToString(),
                    DisplayName = new LocalizableString(typeof(GeoNodeResource), node.Name).Localize(StringLocalizerFactory),
                    ParentId = node.ParentNodeId,
                    ParentName = tree.SingleOrDefault(i => i.Id == node.ParentNodeId)?.Name
                });
            }

            result.Add(new GeoNodeTreeDto
            {
                TreeId = tree.Key,
                CountryName = tree.Single(n => n.NodeType == GeoNodeType.Country).Name,
                GeoNodes = nodeListDto
            });
        }

        return result;
    }
}