using GoTrack.Localization;
using GoTrack.TrackAccounts;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile;

/* Inherit your application services from this class.
 */
public abstract class GoTrackMobileAppService : ApplicationService
{
    protected GoTrackMobileAppService()
    {
        LocalizationResource = typeof(GoTrackResource);
    }

    protected ICurrentTrackAccount CurrentTrackAccount =>
        LazyServiceProvider.LazyGetRequiredService<ICurrentTrackAccount>();
}