using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests;
using GoTrack.Mobile.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Mobile.Requests.RenewTrackAccountSubscriptions.DTOs;
using GoTrack.Mobile.UserTrackAccountAssociations.DTOs;
using GoTrack.Mobile.Vehicles.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PromoCodes;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Settings;
using GoTrack.SmsBundles;
using GoTrack.SubscriptionPlans;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.Vehicles;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;
using Volo.Abp.Uow;
using Volo.Abp.Validation;

namespace GoTrack.Mobile.Requests.RenewSubscriptionRequests;

public class RenewSubscriptionRequestAppService : GoTrackMobileAppService, IRenewSubscriptionRequestAppService
{
    private readonly IRenewSubscriptionRequestManager _renewSubscriptionRequestManager;
    private readonly IRepository<SmsBundle, Guid> _smsBundleRepository;
    private readonly IUserFatoraPaymentManager _userFatoraPaymentManager;
    private readonly BillManager _billManager;
    private readonly IRepository<RenewSubscriptionRequest, Guid> _renewSubscriptionRequestRepository;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly ISettingProvider _settingProvider;
    protected IRepository<Vehicle, Guid> VehicleRepository =>
    LazyServiceProvider.LazyGetRequiredService<IRepository<Vehicle, Guid>>();

    protected IRepository<UserTrackAccountAssociation, Guid> UserTrackAccountAssociationRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<UserTrackAccountAssociation, Guid>>();

    protected RenewSubscriptionRequestBillPlanFactory RenewSubscriptionRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<RenewSubscriptionRequestBillPlanFactory>();

    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();
    
    protected SubscriptionPlanDefinitionStore SubscriptionPlanDefinitionStore 
        => LazyServiceProvider.LazyGetRequiredService<SubscriptionPlanDefinitionStore>();
    protected VehicleCountCalculator VehicleCountCalculator 
        => LazyServiceProvider.LazyGetRequiredService<VehicleCountCalculator>();
    
    public RenewSubscriptionRequestAppService(
        IRenewSubscriptionRequestManager renewSubscriptionRequestManager,
        IRepository<SmsBundle, Guid> smsBundleRepository,
        IUserFatoraPaymentManager userFatoraPaymentManager,
        BillManager billManager,
        IRepository<RenewSubscriptionRequest, Guid> renewSubscriptionRequestRepository,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository, ISettingProvider settingProvider)
    {
        _renewSubscriptionRequestManager = renewSubscriptionRequestManager;
        _smsBundleRepository = smsBundleRepository;
        _userFatoraPaymentManager = userFatoraPaymentManager;
        _billManager = billManager;
        _renewSubscriptionRequestRepository = renewSubscriptionRequestRepository;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _settingProvider = settingProvider;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<Guid> CreateAsync(CreateRenewTrackAccountSubscriptionsDto dto)
    {
        var request = await CraeteRequestAsync(
            dto.SubscriptionPlanKey,
            dto.UserCount,
            dto.SmsBundleId,
            dto.NewVehicles,
            dto.RemoveVehicles,
            dto.RemoveUsers,
            TrackerInstallationLocation.OnSite,
            dto.SubscriptionDurationInMonths,
            dto.PromoCode
        );

        return request.Id;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<BillDto> CreateTempBillAsync(CreateRenewTrackAccountSubscriptionsDto dto)
    {
        if (!string.IsNullOrEmpty(dto.PromoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(dto.PromoCode);
        }
        
        var newTrackVehicles = new List<SubscriptionVehicleInfo>();

        if (dto.NewVehicles is not null)
        {
            newTrackVehicles = dto.NewVehicles.Select(v => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(v.LicensePlateSubClass, v.LicensePlateSerial),
                TryGetColor(v.Color),
                v.ConsumptionRate,
                v.NeedsTrackingDevice)
            ).ToList();
        }

        var subscriptionPlanDefinition = await SubscriptionPlanDefinitionStore.GetWithValidate(dto.SubscriptionPlanKey);

        var vehicleCounts = await VehicleCountCalculator.CalculateCountsAsync(
            newTrackVehicles,
            dto.RemoveVehicles);

        var billPlan = await RenewSubscriptionRequestBillPlanFactory.GenerateBillPlan(
            new RenewSubscriptionRequestBillPlanInput(
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                subscriptionPlanDefinition,
                dto.SubscriptionDurationInMonths,
                vehicleCounts.DevicesCount,
                vehicleCounts.CurrentVehicleCount,
                vehicleCounts.NumberOfAddedCars,
                dto.SmsBundleId,
                dto.PromoCode
            )
        );

        var bill = await _billManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.RenewTrackAccountSubscriptionFatoraPayEnabled,
            GoTrackSettings.RenewTrackAccountSubscriptionPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        
        var request = await _renewSubscriptionRequestRepository.GetAsync(input.RequestId);

        if (request.RenewSubscriptionRequestStage is not RenewSubscriptionRequestStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await _billManager.GetByRequestIdAsync(request.Id);

        return await _userFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<RenewSubscriptionRequestDetailsDto> GetAsync(Guid id)
    {
        var trackAccountId = CurrentTrackAccount.GetId();
        var request = (await _renewSubscriptionRequestRepository.WithDetailsAsync(r => r.Owner))
                      .SingleOrDefault(r =>
                          r.Id == id
                          && r.TrackAccountId == trackAccountId)
                      ?? throw new EntityNotFoundException(typeof(RenewSubscriptionRequest), id);

        var userFatoraPayment = (await _userFatoraPaymentRepository
                .GetQueryableAsync())
            .Where(p => p.Id == id && p.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(p => p.CreationTime)
            .FirstOrDefault();

        var requestDetailsDto = ObjectMapper.Map<RenewSubscriptionRequest, RenewSubscriptionRequestDetailsDto>(request);
        requestDetailsDto.PaymentUrl = userFatoraPayment?.Url;
        return requestDetailsDto;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<RenewSubscriptionRequestDto>> GetListAsync(
        PagedResultRequestDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var query = await _renewSubscriptionRequestRepository.WithDetailsAsync(request => request.Owner);
        query = query.Where(r => r.TrackAccountId == trackAccountId);
        var count = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var requests = await AsyncExecuter.ToListAsync(query);

        var requestDtos =
            ObjectMapper.Map<List<RenewSubscriptionRequest>, List<RenewSubscriptionRequestDto>>(requests);

        return new PagedResultDto<RenewSubscriptionRequestDto>(count, requestDtos);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<VehicleDto>> GetListRemovedVehiclesAsync(Guid id, PagedResultRequestDto input)
    {

        var request = await _renewSubscriptionRequestRepository.GetAsync(id);

        var vehicleQuery = await VehicleRepository.GetQueryableAsync();

        vehicleQuery = vehicleQuery.Where(vehicle => request.RemoveTrackVehicles.Contains(vehicle.Id));

        var totalCount = await AsyncExecuter.CountAsync(vehicleQuery);

        vehicleQuery = vehicleQuery.PageBy(input);

        var vehicleInfos = await AsyncExecuter.ToListAsync(vehicleQuery);

        var vehicleInfoDtos = ObjectMapper.Map<List<Vehicle>, List<VehicleDto>>(vehicleInfos);

        return new PagedResultDto<VehicleDto>(totalCount, vehicleInfoDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<SubscriptionVehicleInfoDto>> GetListNewVehiclesAsync(Guid id, PagedResultRequestDto input)
    {
        var request = await _renewSubscriptionRequestRepository.GetAsync(id);

        //var query = renewSubscriptionRequestQuery.Where(r => r.Id == id)
        //    .SelectMany(x => x.NewTrackVehicles)
        //    .Where(v => v is SubscriptionVehicleInfo)
        //    .Cast<SubscriptionVehicleInfo>();

        //var totalCount = await AsyncExecuter.CountAsync(query);

        //query = query.PageBy(input);

        //var vehicleInfos = await AsyncExecuter.ToListAsync(query);

        //TODO
        var totalCount = request.NewTrackVehicles.Count;
        var vehicleInfos = request.NewTrackVehicles.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

        var vehicleInfoDtos = ObjectMapper.Map<List<SubscriptionVehicleInfo>, List<SubscriptionVehicleInfoDto>>(vehicleInfos);

        return new PagedResultDto<SubscriptionVehicleInfoDto>(totalCount, vehicleInfoDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<UserTrackAccountAssociationDto>> GetListRemovedUsersAsync(Guid id, PagedResultRequestDto input)
    {
        var request = await _renewSubscriptionRequestRepository.GetAsync(id);

        var userTrackAccountAssociationQuery = await UserTrackAccountAssociationRepository.GetQueryableAsync();

        userTrackAccountAssociationQuery = userTrackAccountAssociationQuery
            .Where(x =>
                request.RemoveUsers.Contains(x.Id)
            );

        var totalCount = await AsyncExecuter.CountAsync(userTrackAccountAssociationQuery);

        userTrackAccountAssociationQuery = userTrackAccountAssociationQuery.PageBy(input);

        var userTrackAccountAssociation = await AsyncExecuter.ToListAsync(userTrackAccountAssociationQuery);

        var userTrackAccountAssociationDtos = ObjectMapper
            .Map<List<UserTrackAccountAssociation>, List<UserTrackAccountAssociationDto>>(userTrackAccountAssociation);

        return new PagedResultDto<UserTrackAccountAssociationDto>(totalCount, userTrackAccountAssociationDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<VehicleDto>> GetListRemainingVehiclesAsync(Guid id, PagedResultRequestDto input)
    {
        var request = await _renewSubscriptionRequestRepository.GetAsync(id);

        var vehicleQuery = await VehicleRepository.GetQueryableAsync();

        var trackAccountVehicleQuery = vehicleQuery.Where(vehicle => vehicle.TrackAccountId == CurrentTrackAccount.GetId());

        var removedVehicleQuery = vehicleQuery.Where(vehicle => request.RemoveTrackVehicles.Contains(vehicle.Id));

        var remainingVehicleQuery = trackAccountVehicleQuery.Except(removedVehicleQuery);

        var totalCount = await AsyncExecuter.CountAsync(remainingVehicleQuery);

        remainingVehicleQuery = remainingVehicleQuery.PageBy(input);

        var remainingVehicleInfos = await AsyncExecuter.ToListAsync(remainingVehicleQuery);

        var remainingVehicleInfoDtos = ObjectMapper.Map<List<Vehicle>, List<VehicleDto>>(remainingVehicleInfos);

        return new PagedResultDto<VehicleDto>(totalCount, remainingVehicleInfoDtos);
    }


    private async Task<RenewSubscriptionRequest> CraeteRequestAsync(
        string subscriptionPlanKey,
        int userCount,
        Guid? smsBundleId,
        List<SubscriptionVehicleInfoCreateDto>? newVehicles,
        List<Guid> removeVehicles,
        List<Guid> removeUsers,
        TrackerInstallationLocation? trackerInstallationLocation,
        int subscriptionDurationInMonths,
        string? promoCode = null)
    {
        if (smsBundleId is not null)
            await _smsBundleRepository.GetAsync((Guid)smsBundleId);

        var newTrackVehicles = new List<SubscriptionVehicleInfo>();
        if (newVehicles is not null)
        {
            newTrackVehicles = newVehicles.Select(v => new SubscriptionVehicleInfo(
                    new SubscriptionVehicleLicensePlate(v.LicensePlateSubClass, v.LicensePlateSerial),
                    TryGetColor(v.Color),
                    v.ConsumptionRate,
                    v.NeedsTrackingDevice)
            ).ToList();
        }

        var request = await _renewSubscriptionRequestManager.CreateAsync(CurrentUser.Id!.Value,
            CurrentTrackAccount.GetId(),
            subscriptionPlanKey,
            userCount,
            smsBundleId,
            newTrackVehicles.IsNullOrEmpty() ? null : newTrackVehicles,
            removeVehicles,
            removeUsers,
            trackerInstallationLocation,
            subscriptionDurationInMonths,
            promoCode
        );
        
        return request;
    }

    private static Color TryGetColor(string color)
    {
        try
        {
            return ColorTranslator.FromHtml(color);
        }
        catch (Exception)
        {
            throw new AbpValidationException("", new List<ValidationResult>()
            {
                new("Invalid Color")
            });
        }
    }
}