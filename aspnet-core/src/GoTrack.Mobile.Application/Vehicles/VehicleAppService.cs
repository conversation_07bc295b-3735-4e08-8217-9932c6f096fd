using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Mobile.Vehicles.DTOs;
using GoTrack.Observations;
using GoTrack.Observations.ObservationViewModels;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Mobile.Vehicles;

public class VehicleAppService : GoTrackMobileAppService, IVehicleAppService
{
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly IRepository<VehicleGroup, Guid> _vehicleGroupRepository;
    private readonly IRepository<VehicleGroupVehicle, Guid> _vehicleGroupVehicleRepository;

    protected IRepository<UserTrackAccountAssociation, Guid> UserTrackAccountAssociationRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<UserTrackAccountAssociation, Guid>>();

    protected IRepository<ObservationVehicle, Guid> ObservationVehicleRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<ObservationVehicle, Guid>>();

    protected IRepository<ObservationVehicleGroup, Guid> ObservationVehicleGroupRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<ObservationVehicleGroup, Guid>>();

    protected IObservationReadRepository ObservationReadRepository =>
        LazyServiceProvider.GetRequiredService<IObservationReadRepository>();

    public VehicleAppService(IRepository<Vehicle, Guid> vehicleRepository,
        IRepository<VehicleGroup, Guid> vehicleGroupRepository,
        IRepository<VehicleGroupVehicle, Guid> vehicleGroupVehicleRepository)
    {
        _vehicleRepository = vehicleRepository;
        _vehicleGroupRepository = vehicleGroupRepository;
        _vehicleGroupVehicleRepository = vehicleGroupVehicleRepository;
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<VehicleDto> GetAsync(Guid id)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var userTrackAccountAssociation = await UserTrackAccountAssociationRepository
            .GetAsync(x =>
                x.TrackAccountId == trackAccountId
                && x.UserId == CurrentUser.Id
            );

        if (userTrackAccountAssociation.AssociationType is AssociationType.Observer)
            return await GetObservationVehicleAsync(id, userTrackAccountAssociation);

        var vehicle = await _vehicleRepository.GetAsync(id);

        return ObjectMapper.Map<Vehicle, VehicleDto>(vehicle);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<List<VehicleViewModelWithAuditingDto>> GetListVehicleViewModelWithAuditingAsync()
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var userTrackAccountAssociation = await UserTrackAccountAssociationRepository
            .GetAsync(x =>
                x.TrackAccountId == trackAccountId
                && x.UserId == CurrentUser.Id
            );

        if (userTrackAccountAssociation.AssociationType is AssociationType.Observer)
            return await GetObservationListVehicleIdWithLicensePlateAsync(userTrackAccountAssociation.Id);

        var query = await _vehicleRepository.GetQueryableAsync();

        var vehicles = await AsyncExecuter.ToListAsync(query);

        var vehicleIdWithLicensePlateViewModels = GetVehicleIdWithLicensePlateViewModels(vehicles).ToList();

        return ObjectMapper.Map<List<VehicleViewModel>, List<VehicleViewModelWithAuditingDto>>(vehicleIdWithLicensePlateViewModels);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<PagedResultDto<VehicleDto>> GetListAsync(GetVehicleListRequestDto requestDto)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var userTrackAccountAssociation = await UserTrackAccountAssociationRepository
            .GetAsync(x => 
                x.TrackAccountId == trackAccountId
                && x.UserId == CurrentUser.Id
            );
        
        if (userTrackAccountAssociation.AssociationType is AssociationType.Observer)
            return await GetObservationVehicleListAsync(requestDto, userTrackAccountAssociation);

        var query = await _vehicleRepository.GetQueryableAsync();

        if (requestDto.VehicleGroupId != null)
            query = query.Where(vehicle =>
                vehicle.VehicleGroupVehicles.Any(vg => vg.VehicleGroupId == requestDto.VehicleGroupId));

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(requestDto);

        var vehicles = await AsyncExecuter.ToListAsync(query);
        var vehicleDtos = ObjectMapper.Map<List<Vehicle>, List<VehicleDto>>(vehicles);

        return new PagedResultDto<VehicleDto>(totalCount, vehicleDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task AddAddToVehicleGroup(Guid id, Guid vehicleGroupId)
    {
        Vehicle vehicle = await _vehicleRepository.GetAsync(id);

        VehicleGroup vehicleGroup = await _vehicleGroupRepository.GetAsync(vehicleGroupId);

        var newVehicleGroupVehicle = new VehicleGroupVehicle(GuidGenerator.Create(), vehicle.Id, vehicleGroup.Id);

        await _vehicleGroupVehicleRepository.InsertAsync(newVehicleGroupVehicle);

        await _vehicleRepository.UpdateAsync(vehicle);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task RemoveRemoveFromVehicleGroup(Guid id, Guid vehicleGroupId)
    {
        Vehicle vehicle = await _vehicleRepository.GetAsync(id);

        VehicleGroup vehicleGroup = await _vehicleGroupRepository.GetAsync(vehicleGroupId);

        vehicle.RemoveFromGroup(vehicleGroup.Id);

        await _vehicleRepository.UpdateAsync(vehicle);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task UpdateUpdateConsumptionRate(Guid id, double consumptionRate)
    {
        Vehicle vehicle = await _vehicleRepository.GetAsync(id);
        vehicle.UpdateConsumptionRate(consumptionRate);
    }

    private async Task<VehicleDto> GetObservationVehicleAsync(Guid vehicleId, UserTrackAccountAssociation userTrackAccountAssociation)
    {
        var queryable = await ObservationVehicleRepository.WithDetailsAsync(x => x.Vehicle);
        queryable = queryable.Where(x =>
            x.UserTrackAccountAssociationId == userTrackAccountAssociation.Id &&
            x.VehicleId == vehicleId
        );

        var observationVehicle = await AsyncExecuter.FirstOrDefaultAsync(queryable);
        
        if (observationVehicle is null)
            throw new EntityNotFoundException(nameof(ObservationVehicle));

        return ObjectMapper.Map<Vehicle, VehicleDto>(observationVehicle.Vehicle);
    }

    private async Task<PagedResultDto<VehicleDto>> GetObservationVehicleListAsync(GetVehicleListRequestDto requestDto, UserTrackAccountAssociation userTrackAccountAssociation)
    {
        if (requestDto.VehicleGroupId is null)
        {
            var observationVehicleQuery = await ObservationVehicleRepository.WithDetailsAsync(x => x.Vehicle);

            observationVehicleQuery = observationVehicleQuery.Where(observer =>
                observer.UserTrackAccountAssociationId == userTrackAccountAssociation.Id
            );

            var observerVehiclesTotalCount = await AsyncExecuter.CountAsync(observationVehicleQuery);

            observationVehicleQuery = observationVehicleQuery.PageBy(requestDto);

            var observerVehicles = await AsyncExecuter.ToListAsync(observationVehicleQuery);

            var projectedVehicles = observerVehicles.Select(x => x.Vehicle).ToList();

            var mapedVehicles = ObjectMapper.Map<List<Vehicle>, List<VehicleDto>>(projectedVehicles);

            return new PagedResultDto<VehicleDto>(observerVehiclesTotalCount, mapedVehicles);
        }

        var observationVehicleGroupQuery = await ObservationVehicleGroupRepository.GetQueryableAsync();

        observationVehicleGroupQuery = observationVehicleGroupQuery.Where(observer =>
            observer.UserTrackAccountAssociationId == userTrackAccountAssociation.Id
            && observer.VehicleGroupId == requestDto.VehicleGroupId
        );

        var hasAccess = await AsyncExecuter.AnyAsync(observationVehicleGroupQuery);
        if (!hasAccess)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.NoAccess]);

        var vehicleQuery = await _vehicleRepository.GetQueryableAsync();

        vehicleQuery = vehicleQuery.Where(vehicle =>
            vehicle.VehicleGroupVehicles.Any(vg => vg.VehicleGroupId == requestDto.VehicleGroupId)
        );

        var vehiclesTotalCount = await AsyncExecuter.CountAsync(vehicleQuery);

        vehicleQuery = vehicleQuery.PageBy(requestDto);

        var observationVehicles = await AsyncExecuter.ToListAsync(vehicleQuery);
        var vehiclesDtos = ObjectMapper.Map<List<Vehicle>, List<VehicleDto>>(observationVehicles);

        return new PagedResultDto<VehicleDto>(vehiclesTotalCount, vehiclesDtos);
    }

    private async Task<List<VehicleViewModelWithAuditingDto>> GetObservationListVehicleIdWithLicensePlateAsync(Guid userTrackAccountAssociation)
    {
        var vehicleIdWithLicensePlateViewModels = await ObservationReadRepository.GetObserverVehiclesIdWithLicensePlateAsync(userTrackAccountAssociation);

        return ObjectMapper.Map<List<VehicleViewModel>, List<VehicleViewModelWithAuditingDto>>(vehicleIdWithLicensePlateViewModels);
    }

    private IEnumerable<VehicleViewModel> GetVehicleIdWithLicensePlateViewModels(List<Vehicle> vehicles)
    {
        foreach (var vehicle in vehicles) 
        {
            yield return VehicleViewModel.GetVehicleViewModelFromVehicle(vehicle);
        }
    }
}