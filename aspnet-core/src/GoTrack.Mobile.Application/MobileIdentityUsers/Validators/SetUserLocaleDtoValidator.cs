using FluentValidation;
using GoTrack.Identity;
using GoTrack.Localization;
using GoTrack.Mobile.MobileIdentityUsers.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Mobile.MobileIdentityUsers.Validators;

public class SetUserLocaleDtoValidator : AbstractValidator<SetUserLocaleDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public SetUserLocaleDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.Language)
            .Must(SupportedLanguageExtensions.IsValidLanguageCode)
            .WithMessage(_localizer[GoTrackDomainErrorCodes.InvalidLanguageCode])
            .WithName(_localizer["GoTrack:Language"]);
    }
}