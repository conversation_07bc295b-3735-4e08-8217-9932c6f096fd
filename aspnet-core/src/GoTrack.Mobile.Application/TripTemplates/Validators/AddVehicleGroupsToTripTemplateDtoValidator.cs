using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.TripTemplates.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Mobile.TripTemplates.Validators;

public class AddVehicleGroupsToTripTemplateDtoValidator : AbstractValidator<AddVehicleGroupsToTripTemplateDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public AddVehicleGroupsToTripTemplateDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.TripTemplateId)
            .NotEmpty()
            .WithName(_localizer["GoTrack:TripTemplateId"]);

        RuleFor(x => x.VehicleGroupIds)
            .NotEmpty()
            .WithName(_localizer["GoTrack:VehicleGroupIds"]);
    }
}
