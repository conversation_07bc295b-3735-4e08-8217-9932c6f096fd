using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using GoTrack.Mobile.Options;
using GoTrack.Msisdns;
using GoTrack.SMSMessages;
using GoTrack.Totps;
using Volo.Abp;
using Volo.Abp.EventBus.Distributed;

namespace GoTrack.Mobile.Otps;

public class OtpAppService : GoTrackMobileAppService, IOtpAppService
{
    private readonly MsisdnManager _msisdnManager;
    private readonly ITotpManager _totpManager;
    private readonly DevelopmentOptions _options;
    private readonly IDistributedEventBus _eventBus;

    public OtpAppService(MsisdnManager msisdnManager, ITotpManager totpManager, IOptions<DevelopmentOptions> options, IDistributedEventBus eventBus)
    {
        _msisdnManager = msisdnManager;
        _totpManager = totpManager;
        _eventBus = eventBus;
        _options = options.Value;
    }

    [AllowAnonymous]
    public async Task SendOtpAsync(GenerateOtpDto dto)
    {
        var msisdn = _msisdnManager.Create(dto.msisdn);
        var otp = _totpManager.GenerateCode(msisdn);
        
        if (IsSyrianNumber(msisdn.ToString()))
        {
            await _eventBus.PublishAsync(new SendOtpEto()
            {
                PhoneNumber = msisdn.ToString(),
                Otp = otp
            });
        }
    }
    
    [AllowAnonymous]
    public async Task<string> SendOtpDev(GenerateOtpDto dto)
    {
        if (!_options.EnableDevOtp)
        {
            await SendOtpAsync(dto);
            return string.Empty;
        }
        
        var msisdn = _msisdnManager.Create(dto.msisdn);
        
        return _totpManager.GenerateCode(msisdn);
    }
    
    private bool IsSyrianNumber(string msisdn)
    {
        return (msisdn.Length == 12 && msisdn.StartsWith("9639")) || 
               (msisdn.Length == 14 && msisdn.StartsWith("009639"));
    }
}
