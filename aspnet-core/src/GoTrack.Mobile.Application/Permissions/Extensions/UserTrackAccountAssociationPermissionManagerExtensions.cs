using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using GoTrack.Mobile.Permissions.ValueProviders;
using Volo.Abp;
using Volo.Abp.PermissionManagement;

namespace GoTrack.Mobile.Permissions.Extensions;

public static class UserTrackAccountAssociationPermissionManagerExtensions
{
    public static Task<List<PermissionWithGrantedProviders>> GetAllForUserTrackAccountAssociationAsync([NotNull] this IPermissionManager permissionManager, (Guid trackAccount,Guid userId) id)
    {
        Check.NotNull(permissionManager, nameof(permissionManager));

        return permissionManager.GetAllAsync(UserTrackAccountAssociationPermissionValueProvider.ProviderName, id.trackAccount.ToString() + id.userId.ToString());
    }

    public static Task SetForTrackAccountAssociationAsync([NotNull] this IPermissionManager permissionManager, (Guid trackAccount,Guid userId) id, [NotNull] string name, bool isGranted)
    {
        Check.NotNull(permissionManager, nameof(permissionManager));

        return permissionManager.SetAsync(name, UserTrackAccountAssociationPermissionValueProvider.ProviderName, id.trackAccount.ToString() + id.userId.ToString(), isGranted);
    }
}