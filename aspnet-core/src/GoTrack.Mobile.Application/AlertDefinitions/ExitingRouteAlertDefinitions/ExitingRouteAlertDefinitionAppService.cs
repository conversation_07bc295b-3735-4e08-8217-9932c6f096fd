using GoTrack.AlertDefinitions;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Mobile.AlertDefinitions.ExitingRouteAlertDefinitions.DTOs;
using GoTrack.Mobile.Routes.DTOs;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using GoTrack.AlertDefinitions.RouteAlertDefinitions.RouteAlertRoutes;
using GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions;
using System.Collections.Generic;
using System.Linq;
using GoTrack.Routes;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Features;

namespace GoTrack.Mobile.AlertDefinitions.ExitingRouteAlertDefinitions;

[RequiresFeature(GoTrackFeatureDefinitions.ExitingRouteAlert)]
public class ExitingRouteAlertDefinitionAppService : GoTrackMobileAppService, IExitingRouteAlertDefinitionAppService
{
    private readonly ExitingRouteAlertDefinitionManager _exitingRouteAlertDefinitionManager;
    private readonly IRepository<ExitingRouteAlertDefinition, Guid> _exitingRouteAlertDefinitionRepository;
    private readonly IRepository<RouteAlertRoute, Guid> _routeAlertRouteRepository;
    private readonly IAlertTriggerManagerResolver _alertTriggerManagerResolver;

    public ExitingRouteAlertDefinitionAppService(
        ExitingRouteAlertDefinitionManager exitingRouteAlertDefinitionManager,
        IRepository<ExitingRouteAlertDefinition, Guid> exitingRouteAlertDefinitionRepository,
        IRepository<RouteAlertRoute, Guid> routeAlertRouteRepository,
        IAlertTriggerManagerResolver alertTriggerManagerResolver)
    {
        _exitingRouteAlertDefinitionManager = exitingRouteAlertDefinitionManager;
        _exitingRouteAlertDefinitionRepository = exitingRouteAlertDefinitionRepository;
        _routeAlertRouteRepository = routeAlertRouteRepository;
        _alertTriggerManagerResolver = alertTriggerManagerResolver;
    }


    [Authorize]
    [TrackAccountAuthorize]
    public virtual async Task CreateAsync(CreateExitingRouteAlertDefinitionDto input)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var notificationMethods = new List<AlertDefinitionNotificationMethod>();
        foreach (string notificationMethod in input.NotificationMethods)
        {
            notificationMethods.Add(
                EnumHelper.GetEnumValueByName<AlertDefinitionNotificationMethod>(notificationMethod)
            );
        }

        await _exitingRouteAlertDefinitionManager.CreateAsync(
            input.RouteIds,
            notificationMethods,
            trackAccountId,
            input.VehicleIds,
            input.VehicleGroupIds
        );
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<ExitingRouteAlertDefinitionDto> GetAsync(Guid id)
    {
        var exitingRouteAlertDefinition = await _exitingRouteAlertDefinitionRepository.GetAsync(id, includeDetails: false);

        return ObjectMapper.Map<ExitingRouteAlertDefinition, ExitingRouteAlertDefinitionDto>(exitingRouteAlertDefinition);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<ExitingRouteAlertDefinitionDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _exitingRouteAlertDefinitionRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var exitingRouteAlertDefinitions = await AsyncExecuter.ToListAsync(query);

        var exitingRouteAlertDefinitionDtos = ObjectMapper.Map<List<ExitingRouteAlertDefinition>, List<ExitingRouteAlertDefinitionDto>>(exitingRouteAlertDefinitions);

        return new PagedResultDto<ExitingRouteAlertDefinitionDto>(totalCount, exitingRouteAlertDefinitionDtos);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<RouteDto>> GetRoutesAsync(Guid id, PagedResultRequestDto input)
    {
        var query = await _routeAlertRouteRepository.WithDetailsAsync(x => x.Route);

        query = query.Where(x => x.AlertDefinitionId == id);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var routeAlertRoutes = await AsyncExecuter.ToListAsync(query);

        var routes = routeAlertRoutes.Select(x => x.Route).ToList();

        var routeDtos = ObjectMapper.Map<List<Route>, List<RouteDto>>(routes);

        return new PagedResultDto<RouteDto>(totalCount, routeDtos);
    }
}
