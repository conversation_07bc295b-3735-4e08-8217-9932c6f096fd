using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Mobile.AlertDefinitions.ExceedingSpeedAlertDefinitions.DTOs;
using GoTrack.TrackableEntities;
using GoTrack.TrackAccounts;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;

namespace GoTrack.Mobile.AlertDefinitions.ExceedingSpeedAlertDefinitions;

[RequiresFeature(GoTrackFeatureDefinitions.ExceedingSpeedAlert)]
public class ExceedingSpeedAlertDefinitionAppService : GoTrackMobileAppService, IExceedingSpeedAlertDefinitionAppService
{
    private readonly ICurrentTrackAccount _currentTrackAccount;
    private readonly IAlertDefinitionManager _alertDefinitionManager;
    private readonly IRepository<ExceedingSpeedAlertDefinition, Guid> _exceedingSpeedAlertDefinitionRepository;
    private readonly IRepository<AlertDefinitionAssociation, Guid> _alertDefinitionAssociationRepository;

    private readonly IRepository<TrackableEntityAssociation, Guid> _trackableEntityAssociationRepository;
    private readonly IAlertTriggerManagerResolver _alertTriggerManagerResolver;

    public ExceedingSpeedAlertDefinitionAppService(
        ICurrentTrackAccount currentTrackAccount,
        IAlertDefinitionManager alertDefinitionManager,
        IRepository<ExceedingSpeedAlertDefinition, Guid> exceedingSpeedAlertDefinitionRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<TrackableEntityAssociation, Guid> trackableEntityAssociationRepository,
        IAlertTriggerManagerResolver alertTriggerManagerResolver)
    {
        _currentTrackAccount = currentTrackAccount;
        _alertDefinitionManager = alertDefinitionManager;
        _exceedingSpeedAlertDefinitionRepository = exceedingSpeedAlertDefinitionRepository;
        _alertDefinitionAssociationRepository = alertDefinitionAssociationRepository;
        _trackableEntityAssociationRepository = trackableEntityAssociationRepository;
        _alertTriggerManagerResolver = alertTriggerManagerResolver;
    }


    [Authorize]
    [TrackAccountAuthorize]
    public virtual async Task CreateAsync(CreateExceedingSpeedAlertDefinitionDto input)
    {
        var trackAccountId = _currentTrackAccount.GetId();

        var notificationMethods = new List<AlertDefinitionNotificationMethod>();
        foreach (string notificationMethod in input.NotificationMethods)
        {
            notificationMethods.Add(
                EnumHelper.GetEnumValueByName<AlertDefinitionNotificationMethod>(notificationMethod)
            );
        }

        var exceedingSpeedAlertDefinition = await _alertDefinitionManager.CreateExceedingSpeedAlertDefinitionAsync(
            input.MaxSpeed,
            notificationMethods,
            trackAccountId
        );

        var alertDefinitionAssociations = new List<AlertDefinitionAssociation>();
        var trackableEntityAssociations = new List<TrackableEntityAssociation>();

        foreach (var vehicleId in input.VehicleIds)
        {
            var newVehicleTrackableEntityAssociation = new VehicleTrackableEntityAssociation(GuidGenerator.Create(), vehicleId);

            trackableEntityAssociations.Add(newVehicleTrackableEntityAssociation);

            alertDefinitionAssociations.Add(
                new AlertDefinitionAssociation(
                    GuidGenerator.Create(),
                    exceedingSpeedAlertDefinition.Id,
                    newVehicleTrackableEntityAssociation.Id
                )
            );
        }

        foreach (var vehicleGroupId in input.VehicleGroupIds)
        {
            var newVehicleGroupTrackableEntityAssociation = new VehicleGroupTrackableEntityAssociation(GuidGenerator.Create(), vehicleGroupId);

            trackableEntityAssociations.Add(newVehicleGroupTrackableEntityAssociation);

            alertDefinitionAssociations.Add(
                new AlertDefinitionAssociation(
                    GuidGenerator.Create(),
                    exceedingSpeedAlertDefinition.Id,
                    newVehicleGroupTrackableEntityAssociation.Id
                )
            );
        }

        await _trackableEntityAssociationRepository.InsertManyAsync(trackableEntityAssociations, autoSave: true);

        await _alertDefinitionAssociationRepository.InsertManyAsync(alertDefinitionAssociations, autoSave: true);
        
        var alertTriggerManager = _alertTriggerManagerResolver.GetAlertTriggerManager(AlertType.ExceedingSpeed);

        await alertTriggerManager.CreateAsync(exceedingSpeedAlertDefinition.Id);
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<PagedResultDto<ExceedingSpeedAlertDefinitionDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _exceedingSpeedAlertDefinitionRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var exceedingSpeedAlertDefinitions = await AsyncExecuter.ToListAsync(query);

        var exceedingSpeedAlertDefinitionDtos = ObjectMapper.Map<List<ExceedingSpeedAlertDefinition>, List<ExceedingSpeedAlertDefinitionDto>>(exceedingSpeedAlertDefinitions);

        return new PagedResultDto<ExceedingSpeedAlertDefinitionDto>(totalCount, exceedingSpeedAlertDefinitionDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<ExceedingSpeedAlertDefinitionDto> GetAsync(Guid id)
    {
        var exceedingSpeedAlertDefinitionDto = await _exceedingSpeedAlertDefinitionRepository.GetAsync(id);

        return ObjectMapper.Map<ExceedingSpeedAlertDefinition, ExceedingSpeedAlertDefinitionDto>(exceedingSpeedAlertDefinitionDto);
    }
}