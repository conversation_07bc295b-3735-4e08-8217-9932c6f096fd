using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.AlertDefinitions.JobTimeAlertDefinitions.DTOs;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;

namespace GoTrack.Mobile.AlertDefinitions.JobTimeAlertDefinitions.Validators;

public class CreateJobTimeAlertDefinitionDtoValidator : AbstractValidator<CreateJobTimeAlertDefinitionDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public CreateJobTimeAlertDefinitionDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.VehicleIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleGroupIds.IsNullOrEmpty())
            .Must(x => x.Count == x.ToHashSet().Count)
            .WithName(_localizer["GoTrack:VehicleIds"]);

        RuleFor(x => x.VehicleGroupIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleIds.IsNullOrEmpty())
            .Must(x => x.Count == x.ToHashSet().Count)
            .WithName(_localizer["GoTrack:VehicleGroupIds"]);

        RuleFor(x => x.NotificationMethods)
            .Must(x => !x.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:NotificationMethods"]);

        RuleFor(x => x.Name.Trim())
            .NotEmpty()
            .WithName(_localizer["GoTrack:Name"]);

        RuleFor(x => x.StartTime)
            .Must(x => x != default)
            .WithName(_localizer["GoTrack:StratTime"]);

        RuleFor(x => x.EndTime)
            .GreaterThan(x => x.StartTime)
            .Must(x => x != default)
            .WithName(_localizer["GoTrack:EndTime"]);

        RuleFor(x => x.DaysOfWeek)
            .Must(x => !x.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:DaysOfWeek"]);
    }
}
