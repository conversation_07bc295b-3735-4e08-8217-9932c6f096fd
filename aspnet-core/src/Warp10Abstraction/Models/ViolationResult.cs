namespace Warp10Abstraction.Models;

public class ViolationResult
{
    public WarpGTS[] ViolatedIntervals { get; set; }
    public string FirstTimestamp { get; set; }
    public string LastTimestamp { get; set; }

    public string Imei { get; set; }

    public List<KeyValuePair<string, string>> MergedViolatedIntervals;

    public int MergeNearbyAlerts(int mergeIntervalInSeconds)
    {
        MergedViolatedIntervals = new List<KeyValuePair<string, string>>();
        WarpGTS[] orderedResults = ViolatedIntervals.OrderBy(x => long.Parse(x.GetFirstTimestamp()))
            .ToArray();
        foreach (WarpGTS checkResult in orderedResults)
        {
            if (MergedViolatedIntervals.Count == 0)
            {
                MergedViolatedIntervals.Add(new KeyValuePair<string, string>(checkResult.GetFirstTimestamp(),
                    checkResult.GetLastTimestamp()));
                continue;
            }

            if (long.Parse(checkResult.GetFirstTimestamp()) - long.Parse(MergedViolatedIntervals[^1].Value) <=
                mergeIntervalInSeconds * 1000)
            {
                MergedViolatedIntervals[^1] =
                    new KeyValuePair<string, string>(MergedViolatedIntervals[^1].Key, checkResult.GetLastTimestamp());
                continue;
            }

            MergedViolatedIntervals.Add(new KeyValuePair<string, string>(checkResult.GetFirstTimestamp(),
                checkResult.GetLastTimestamp()));
        }

        return MergedViolatedIntervals.Count;
    }
}