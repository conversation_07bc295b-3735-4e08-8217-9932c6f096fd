using Warp10Abstraction.Models;

namespace Warp10Abstraction.Sensors;

public interface ISensorRepository
{
    //TODO change the gts warp structure
    Task<WarpGTS> GetAsync(string imei, Sensor sensor, DateTime from, DateTime to);
    Task<List<WarpGTS>> GetAsync(string imei, List<Sensor> sensors, DateTime from, DateTime to);
    Task<DateTimeOffset> GetLastActivityTimeAsync(string imei, Sensor sensor = Sensor.ServerTime);
    Task<Dictionary<string, WarpGTS[]>> GetLastRecordOfImeis(List<string> imeis, List<Sensor> sensors);
}