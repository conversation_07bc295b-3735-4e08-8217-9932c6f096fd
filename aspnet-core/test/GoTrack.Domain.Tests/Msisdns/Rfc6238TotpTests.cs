using System;
using GoTrack.Totps;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using NSubstitute;
using GoTrack.Totps;
using Shouldly;
using Volo.Abp.Timing;
using Xunit;

namespace GoTrack.Msisdns;

public class Rfc6238TotpTests : GoTrackDomainTestBase
{
    private readonly Rfc6238TotpManager _rfc6238TotpManager;
    private readonly Msisdn _msisdnSample1; 
    private readonly Msisdn _msisdnSample2;
    

    public Rfc6238TotpTests()
    {
        var clock = Substitute.For<IClock>();
        clock.Now.Returns(DateTime.Now);
        
        _rfc6238TotpManager = new Rfc6238TotpManager(clock, new NullLogger<Rfc6238TotpManager>());
        
        _msisdnSample1 = new Msisdn("963", "099", "6346666");
        _msisdnSample2 = new Msisdn("963", "099", "6346667");
    }

    [Fact]
    public void SixCharacterOtpsAreGenerated()
    {
        _rfc6238TotpManager.GenerateCode(_msisdnSample1).Length.ShouldBe(6);
    }
    
    [Fact]
    public void GeneratedOtpsUniquePerMsisdn()
    {
        var code1 = _rfc6238TotpManager.GenerateCode(_msisdnSample1);
        var code2 = _rfc6238TotpManager.GenerateCode(_msisdnSample2);
        code1.ShouldNotBe(code2);
    }
    
    
    [Fact]
    public void GeneratedOtpsShouldBeInstantlyValid()
    {
        var code1 = _rfc6238TotpManager.GenerateCode(_msisdnSample1);
        _rfc6238TotpManager.IsValidCode(_msisdnSample1, code1).ShouldBe(true);
    }

    [Fact]
    public void GeneratedOtpsShouldBeInvalidForWrongPhone()
    {
        var code1 = _rfc6238TotpManager.GenerateCode(_msisdnSample1);
        _rfc6238TotpManager.IsValidCode(_msisdnSample2, code1).ShouldBe(false);
    }

    [Fact]
    public void GeneratedOtps10MinutesApartAreDifferent()
    {
        var code = _rfc6238TotpManager.GenerateCode(_msisdnSample1);

        var futureClock = Substitute.For<IClock>();
        futureClock.Now.Returns(DateTime.Now + TimeSpan.FromMinutes(10));
        var otpManagerInFuture = new Rfc6238TotpManager(futureClock, new NullLogger<Rfc6238TotpManager>());

        var codeAfter10Minutes = otpManagerInFuture.GenerateCode(_msisdnSample1);
        codeAfter10Minutes.ShouldNotBe(code);
    }
    
}